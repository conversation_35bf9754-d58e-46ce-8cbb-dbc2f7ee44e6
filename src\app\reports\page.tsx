'use client'

import { useEffect, useState } from 'react'
import Navigation from '@/components/Navigation'
import { supabase, type Delivery, type Product, type DeliveryItem } from '@/lib/supabase'
import { format, startOfMonth, endOfMonth, subMonths } from 'date-fns'
import { DocumentArrowDownIcon } from '@heroicons/react/24/outline'

interface ReportData {
  totalDeliveries: number
  totalPickups: number
  totalDelivered: number
  totalReturned: number
  successRate: number
  topProducts: { product: Product; totalQuantity: number }[]
  monthlyData: { month: string; pickups: number; delivered: number; returned: number }[]
}

export default function ReportsPage() {
  const [reportData, setReportData] = useState<ReportData>({
    totalDeliveries: 0,
    totalPickups: 0,
    totalDelivered: 0,
    totalReturned: 0,
    successRate: 0,
    topProducts: [],
    monthlyData: []
  })
  const [loading, setLoading] = useState(true)
  const [dateRange, setDateRange] = useState({
    start: format(startOfMonth(subMonths(new Date(), 2)), 'yyyy-MM-dd'),
    end: format(endOfMonth(new Date()), 'yyyy-MM-dd')
  })

  useEffect(() => {
    fetchReportData()
  }, [dateRange])

  const fetchReportData = async () => {
    try {
      // Fetch deliveries within date range
      const { data: deliveries } = await supabase
        .from('deliveries')
        .select('*')
        .gte('date', dateRange.start)
        .lte('date', dateRange.end)
        .order('date', { ascending: false })

      // Fetch delivery items with products
      const { data: deliveryItems } = await supabase
        .from('delivery_items')
        .select(`
          *,
          product:products(*),
          delivery:deliveries(date)
        `)
        .gte('delivery.date', dateRange.start)
        .lte('delivery.date', dateRange.end)

      if (deliveries) {
        const totalPickups = deliveries.reduce((sum, d) => sum + d.pickup_count, 0)
        const totalDelivered = deliveries.reduce((sum, d) => sum + d.delivered_count, 0)
        const totalReturned = deliveries.reduce((sum, d) => sum + d.returned_count, 0)
        const successRate = totalPickups > 0 ? (totalDelivered / totalPickups) * 100 : 0

        // Calculate monthly data
        const monthlyMap = new Map<string, { pickups: number; delivered: number; returned: number }>()
        deliveries.forEach(delivery => {
          const monthKey = format(new Date(delivery.date), 'MMM yyyy')
          const existing = monthlyMap.get(monthKey) || { pickups: 0, delivered: 0, returned: 0 }
          monthlyMap.set(monthKey, {
            pickups: existing.pickups + delivery.pickup_count,
            delivered: existing.delivered + delivery.delivered_count,
            returned: existing.returned + delivery.returned_count
          })
        })

        const monthlyData = Array.from(monthlyMap.entries()).map(([month, data]) => ({
          month,
          ...data
        }))

        // Calculate top products
        const productMap = new Map<string, { product: Product; totalQuantity: number }>()
        if (deliveryItems) {
          deliveryItems.forEach((item: any) => {
            if (item.product) {
              const existing = productMap.get(item.product.id) || { product: item.product, totalQuantity: 0 }
              productMap.set(item.product.id, {
                ...existing,
                totalQuantity: existing.totalQuantity + item.quantity
              })
            }
          })
        }

        const topProducts = Array.from(productMap.values())
          .sort((a, b) => b.totalQuantity - a.totalQuantity)
          .slice(0, 10)

        setReportData({
          totalDeliveries: deliveries.length,
          totalPickups,
          totalDelivered,
          totalReturned,
          successRate,
          topProducts,
          monthlyData
        })
      }
    } catch (error) {
      console.error('Error fetching report data:', error)
    } finally {
      setLoading(false)
    }
  }

  const exportToCSV = () => {
    const csvContent = [
      ['Inventory Management Report'],
      [`Date Range: ${dateRange.start} to ${dateRange.end}`],
      [''],
      ['Summary'],
      ['Total Deliveries', reportData.totalDeliveries],
      ['Total Pickups', reportData.totalPickups],
      ['Total Delivered', reportData.totalDelivered],
      ['Total Returned', reportData.totalReturned],
      ['Success Rate', `${reportData.successRate.toFixed(1)}%`],
      [''],
      ['Monthly Data'],
      ['Month', 'Pickups', 'Delivered', 'Returned'],
      ...reportData.monthlyData.map(item => [item.month, item.pickups, item.delivered, item.returned]),
      [''],
      ['Top Products'],
      ['Product Name', 'SKU', 'Total Quantity'],
      ...reportData.topProducts.map(item => [item.product.name, item.product.sku, item.totalQuantity])
    ].map(row => row.join(',')).join('\n')

    const blob = new Blob([csvContent], { type: 'text/csv' })
    const url = window.URL.createObjectURL(blob)
    const a = document.createElement('a')
    a.href = url
    a.download = `inventory-report-${format(new Date(), 'yyyy-MM-dd')}.csv`
    a.click()
    window.URL.revokeObjectURL(url)
  }

  if (loading) {
    return (
      <div className="min-h-screen bg-gray-50">
        <Navigation />
        <div className="flex items-center justify-center h-96">
          <div className="text-lg">Loading reports...</div>
        </div>
      </div>
    )
  }

  return (
    <div className="min-h-screen bg-gray-50">
      <Navigation />
      
      <div className="max-w-7xl mx-auto py-6 sm:px-6 lg:px-8">
        <div className="px-4 py-6 sm:px-0">
          <div className="flex justify-between items-center mb-8">
            <h1 className="text-3xl font-bold text-gray-900">Reports</h1>
            <button
              onClick={exportToCSV}
              className="bg-green-600 hover:bg-green-700 text-white px-4 py-2 rounded-md flex items-center space-x-2"
            >
              <DocumentArrowDownIcon className="h-5 w-5" />
              <span>Export CSV</span>
            </button>
          </div>

          {/* Date Range Filter */}
          <div className="bg-white shadow rounded-lg mb-8">
            <div className="px-4 py-5 sm:p-6">
              <h3 className="text-lg leading-6 font-medium text-gray-900 mb-4">Date Range</h3>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <label className="block text-sm font-medium text-gray-700">Start Date</label>
                  <input
                    type="date"
                    value={dateRange.start}
                    onChange={(e) => setDateRange(prev => ({ ...prev, start: e.target.value }))}
                    className="mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500"
                  />
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-700">End Date</label>
                  <input
                    type="date"
                    value={dateRange.end}
                    onChange={(e) => setDateRange(prev => ({ ...prev, end: e.target.value }))}
                    className="mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500"
                  />
                </div>
              </div>
            </div>
          </div>

          {/* Summary Stats */}
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-5 gap-6 mb-8">
            <div className="bg-white overflow-hidden shadow rounded-lg">
              <div className="p-5">
                <div className="flex items-center">
                  <div className="flex-shrink-0">
                    <div className="w-8 h-8 bg-blue-500 rounded-md flex items-center justify-center">
                      <span className="text-white font-bold">D</span>
                    </div>
                  </div>
                  <div className="ml-5 w-0 flex-1">
                    <dl>
                      <dt className="text-sm font-medium text-gray-500 truncate">Deliveries</dt>
                      <dd className="text-lg font-medium text-gray-900">{reportData.totalDeliveries}</dd>
                    </dl>
                  </div>
                </div>
              </div>
            </div>

            <div className="bg-white overflow-hidden shadow rounded-lg">
              <div className="p-5">
                <div className="flex items-center">
                  <div className="flex-shrink-0">
                    <div className="w-8 h-8 bg-yellow-500 rounded-md flex items-center justify-center">
                      <span className="text-white font-bold">P</span>
                    </div>
                  </div>
                  <div className="ml-5 w-0 flex-1">
                    <dl>
                      <dt className="text-sm font-medium text-gray-500 truncate">Pickups</dt>
                      <dd className="text-lg font-medium text-gray-900">{reportData.totalPickups}</dd>
                    </dl>
                  </div>
                </div>
              </div>
            </div>

            <div className="bg-white overflow-hidden shadow rounded-lg">
              <div className="p-5">
                <div className="flex items-center">
                  <div className="flex-shrink-0">
                    <div className="w-8 h-8 bg-green-500 rounded-md flex items-center justify-center">
                      <span className="text-white font-bold">✓</span>
                    </div>
                  </div>
                  <div className="ml-5 w-0 flex-1">
                    <dl>
                      <dt className="text-sm font-medium text-gray-500 truncate">Delivered</dt>
                      <dd className="text-lg font-medium text-gray-900">{reportData.totalDelivered}</dd>
                    </dl>
                  </div>
                </div>
              </div>
            </div>

            <div className="bg-white overflow-hidden shadow rounded-lg">
              <div className="p-5">
                <div className="flex items-center">
                  <div className="flex-shrink-0">
                    <div className="w-8 h-8 bg-red-500 rounded-md flex items-center justify-center">
                      <span className="text-white font-bold">↩</span>
                    </div>
                  </div>
                  <div className="ml-5 w-0 flex-1">
                    <dl>
                      <dt className="text-sm font-medium text-gray-500 truncate">Returned</dt>
                      <dd className="text-lg font-medium text-gray-900">{reportData.totalReturned}</dd>
                    </dl>
                  </div>
                </div>
              </div>
            </div>

            <div className="bg-white overflow-hidden shadow rounded-lg">
              <div className="p-5">
                <div className="flex items-center">
                  <div className="flex-shrink-0">
                    <div className="w-8 h-8 bg-purple-500 rounded-md flex items-center justify-center">
                      <span className="text-white font-bold">%</span>
                    </div>
                  </div>
                  <div className="ml-5 w-0 flex-1">
                    <dl>
                      <dt className="text-sm font-medium text-gray-500 truncate">Success Rate</dt>
                      <dd className="text-lg font-medium text-gray-900">{reportData.successRate.toFixed(1)}%</dd>
                    </dl>
                  </div>
                </div>
              </div>
            </div>
          </div>

          <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
            {/* Monthly Data */}
            <div className="bg-white shadow rounded-lg">
              <div className="px-4 py-5 sm:p-6">
                <h3 className="text-lg leading-6 font-medium text-gray-900 mb-4">Monthly Performance</h3>
                {reportData.monthlyData.length > 0 ? (
                  <div className="overflow-x-auto">
                    <table className="min-w-full divide-y divide-gray-200">
                      <thead className="bg-gray-50">
                        <tr>
                          <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Month</th>
                          <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Pickups</th>
                          <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Delivered</th>
                          <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Returned</th>
                        </tr>
                      </thead>
                      <tbody className="bg-white divide-y divide-gray-200">
                        {reportData.monthlyData.map((item, index) => (
                          <tr key={index}>
                            <td className="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">{item.month}</td>
                            <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">{item.pickups}</td>
                            <td className="px-6 py-4 whitespace-nowrap text-sm text-green-600">{item.delivered}</td>
                            <td className="px-6 py-4 whitespace-nowrap text-sm text-red-600">{item.returned}</td>
                          </tr>
                        ))}
                      </tbody>
                    </table>
                  </div>
                ) : (
                  <p className="text-gray-500">No data available for the selected date range.</p>
                )}
              </div>
            </div>

            {/* Top Products */}
            <div className="bg-white shadow rounded-lg">
              <div className="px-4 py-5 sm:p-6">
                <h3 className="text-lg leading-6 font-medium text-gray-900 mb-4">Top Products</h3>
                {reportData.topProducts.length > 0 ? (
                  <div className="overflow-x-auto">
                    <table className="min-w-full divide-y divide-gray-200">
                      <thead className="bg-gray-50">
                        <tr>
                          <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Product</th>
                          <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">SKU</th>
                          <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Total Qty</th>
                        </tr>
                      </thead>
                      <tbody className="bg-white divide-y divide-gray-200">
                        {reportData.topProducts.map((item, index) => (
                          <tr key={index}>
                            <td className="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">{item.product.name}</td>
                            <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">{item.product.sku}</td>
                            <td className="px-6 py-4 whitespace-nowrap text-sm text-blue-600">{item.totalQuantity}</td>
                          </tr>
                        ))}
                      </tbody>
                    </table>
                  </div>
                ) : (
                  <p className="text-gray-500">No product data available for the selected date range.</p>
                )}
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  )
}
