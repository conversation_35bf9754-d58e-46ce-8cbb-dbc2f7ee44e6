'use client'

import { useEffect, useState } from 'react'
import Link from 'next/link'
import Navigation from '@/components/Navigation'
import { supabase, isSupabaseConfigured } from '@/lib/supabase'
import { format } from 'date-fns'
import { BarChart, Bar, XAxis, YAxis, CartesianGrid, Tooltip, ResponsiveContainer } from 'recharts'

interface DashboardStats {
  totalInventoryValue: number
  totalProducts: number
  todayDeliveries: number
  todayReturns: number
  weeklyData: Array<{
    name: string
    deliveries: number
    returns: number
  }>
  topProducts: Array<{
    name: string
    currentInventory: number
    totalCost: number
  }>
  recentDeliveries: Array<{
    date: string
    total_pickup: number
    total_delivered: number
  }>
}

export default function Home() {
  const [stats, setStats] = useState<DashboardStats>({
    totalInventoryValue: 0,
    totalProducts: 0,
    todayDeliveries: 0,
    todayReturns: 0,
    weeklyData: [],
    topProducts: [],
    recentDeliveries: []
  })
  const [loading, setLoading] = useState(true)

  useEffect(() => {
    fetchDashboardData()
  }, [])

  const fetchDashboardData = async () => {
    if (!isSupabaseConfigured) {
      setLoading(false)
      return
    }

    try {
      // Get total inventory value and product count
      const { data: inventoryData } = await supabase
        .from('inventory')
        .select(`
          current_inventory,
          total_cost,
          products (name, cost_per_unit)
        `)

      // Get today's deliveries
      const today = new Date().toISOString().split('T')[0]
      const { data: todayDeliveryData } = await supabase
        .from('daily_deliveries')
        .select('total_delivered')
        .eq('date', today)
        .single()

      // Get today's returns
      const { data: todayReturnData } = await supabase
        .from('returns')
        .select('return_count')
        .eq('date', today)
        .single()

      // Get recent deliveries (last 7 days)
      const weekAgo = new Date()
      weekAgo.setDate(weekAgo.getDate() - 7)
      const { data: recentDeliveries } = await supabase
        .from('daily_deliveries')
        .select('date, total_pickup, total_delivered')
        .gte('date', weekAgo.toISOString().split('T')[0])
        .order('date', { ascending: false })
        .limit(5)

      // Get weekly delivery data for chart
      const { data: weeklyDeliveries } = await supabase
        .from('daily_deliveries')
        .select('date, total_delivered')
        .gte('date', weekAgo.toISOString().split('T')[0])
        .order('date', { ascending: true })

      const { data: weeklyReturns } = await supabase
        .from('returns')
        .select('date, return_count')
        .gte('date', weekAgo.toISOString().split('T')[0])
        .order('date', { ascending: true })

      // Process data
      const totalInventoryValue = inventoryData?.reduce((sum, item) => sum + (item.total_cost || 0), 0) || 0
      const totalProducts = inventoryData?.length || 0

      // Create weekly chart data
      const weeklyData = []
      const days = ['Sun', 'Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat']
      for (let i = 0; i < 7; i++) {
        const date = new Date()
        date.setDate(date.getDate() - (6 - i))
        const dateStr = date.toISOString().split('T')[0]
        const dayName = days[date.getDay()]

        const deliveryData = weeklyDeliveries?.find(d => d.date === dateStr)
        const returnData = weeklyReturns?.find(r => r.date === dateStr)

        weeklyData.push({
          name: dayName,
          deliveries: deliveryData?.total_delivered || 0,
          returns: returnData?.return_count || 0
        })
      }

      // Get top products by inventory value
      const topProducts = inventoryData
        ?.filter(item => item.current_inventory > 0)
        ?.sort((a, b) => (b.total_cost || 0) - (a.total_cost || 0))
        ?.slice(0, 5)
        ?.map(item => ({
          name: item.products?.name || 'Unknown',
          currentInventory: item.current_inventory,
          totalCost: item.total_cost || 0
        })) || []

      setStats({
        totalInventoryValue,
        totalProducts,
        todayDeliveries: todayDeliveryData?.total_delivered || 0,
        todayReturns: todayReturnData?.return_count || 0,
        weeklyData,
        topProducts,
        recentDeliveries: recentDeliveries || []
      })
    } catch (error) {
      console.error('Error fetching dashboard data:', error)
    } finally {
      setLoading(false)
    }
  }

  if (loading) {
    return (
      <div className="min-h-screen bg-gray-50">
        <Navigation />
        <div className="flex items-center justify-center h-96">
          <div className="animate-spin rounded-full h-32 w-32 border-b-2 border-blue-500"></div>
        </div>
      </div>
    )
  }

  if (!isSupabaseConfigured) {
    return (
      <div className="min-h-screen bg-gray-50">
        <Navigation />
        <div className="max-w-4xl mx-auto py-6 sm:px-6 lg:px-8">
          <div className="px-4 py-6 sm:px-0">
            <div className="bg-white shadow rounded-lg">
              <div className="px-4 py-5 sm:p-6">
                <h1 className="text-3xl font-bold text-gray-900 mb-6">Welcome to Cosmetics Inventory Management!</h1>

                <div className="bg-blue-50 border border-blue-200 rounded-md p-6 mb-6">
                  <h2 className="text-xl font-semibold text-blue-900 mb-4">🚀 Setup Required</h2>
                  <p className="text-blue-800 mb-4">
                    To get started with your cosmetics inventory management system, you need to configure your Supabase database.
                  </p>

                  <div className="space-y-4">
                    <div>
                      <h3 className="font-semibold text-blue-900">Step 1: Create Supabase Account</h3>
                      <p className="text-blue-700">Go to <a href="https://supabase.com" target="_blank" rel="noopener noreferrer" className="underline">supabase.com</a> and create a free account</p>
                    </div>

                    <div>
                      <h3 className="font-semibold text-blue-900">Step 2: Create Database</h3>
                      <p className="text-blue-700">Create a new project and run the SQL commands from <code className="bg-blue-100 px-1 py-0.5 rounded">database-setup.sql</code></p>
                    </div>

                    <div>
                      <h3 className="font-semibold text-blue-900">Step 3: Configure Environment</h3>
                      <p className="text-blue-700">Update your <code className="bg-blue-100 px-1 py-0.5 rounded">.env.local</code> file with your Supabase credentials</p>
                    </div>

                    <div>
                      <h3 className="font-semibold text-blue-900">Step 4: Restart Server</h3>
                      <p className="text-blue-700">Restart your development server to apply the changes</p>
                    </div>
                  </div>
                </div>

                <div className="bg-green-50 border border-green-200 rounded-md p-4">
                  <h3 className="font-semibold text-green-900 mb-2">✨ What you'll get:</h3>
                  <ul className="text-green-800 space-y-1">
                    <li>• Daily delivery tracking with product quantities</li>
                    <li>• Returns management system</li>
                    <li>• Inventory tracking with cost calculations</li>
                    <li>• Monthly purchase history</li>
                    <li>• Expense management</li>
                    <li>• Financial reports and analytics</li>
                  </ul>
                </div>

                <div className="mt-6">
                  <p className="text-gray-600">
                    Need help? Check the <strong>Settings</strong> page for detailed setup instructions, or refer to the <code className="bg-gray-100 px-1 py-0.5 rounded">README.md</code> file.
                  </p>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    )
  }

  return (
    <div className="min-h-screen bg-gray-50">
      <Navigation />

      <div className="max-w-7xl mx-auto py-6 sm:px-6 lg:px-8">
        <div className="px-4 py-6 sm:px-0">
          {/* Header */}
          <div className="bg-white shadow rounded-lg p-6 mb-6">
            <h1 className="text-3xl font-bold text-gray-900">Cosmetics Inventory Management</h1>
            <p className="mt-2 text-gray-600">Track your deliveries, returns, and inventory in real-time</p>
          </div>

          {/* Quick Stats */}
          <div className="grid grid-cols-1 md:grid-cols-4 gap-6 mb-6">
            <div className="bg-white overflow-hidden shadow rounded-lg">
              <div className="p-5">
                <div className="flex items-center">
                  <div className="flex-shrink-0">
                    <div className="w-8 h-8 bg-blue-500 rounded-md flex items-center justify-center">
                      <svg className="w-5 h-5 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M20 7l-8-4-8 4m16 0l-8 4m8-4v10l-8 4m0-10L4 7m8 4v10M4 7v10l8 4" />
                      </svg>
                    </div>
                  </div>
                  <div className="ml-5 w-0 flex-1">
                    <dl>
                      <dt className="text-sm font-medium text-gray-500 truncate">Today's Deliveries</dt>
                      <dd className="text-lg font-medium text-gray-900">{stats.todayDeliveries}</dd>
                    </dl>
                  </div>
                </div>
              </div>
            </div>

            <div className="bg-white overflow-hidden shadow rounded-lg">
              <div className="p-5">
                <div className="flex items-center">
                  <div className="flex-shrink-0">
                    <div className="w-8 h-8 bg-red-500 rounded-md flex items-center justify-center">
                      <svg className="w-5 h-5 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M16 15v-1a4 4 0 00-4-4H8m0 0l3 3m-3-3l3-3m9 14V5a2 2 0 00-2-2H6a2 2 0 00-2 2v16l4-2 4 2 4-2 4 2z" />
                      </svg>
                    </div>
                  </div>
                  <div className="ml-5 w-0 flex-1">
                    <dl>
                      <dt className="text-sm font-medium text-gray-500 truncate">Today's Returns</dt>
                      <dd className="text-lg font-medium text-gray-900">{stats.todayReturns}</dd>
                    </dl>
                  </div>
                </div>
              </div>
            </div>

            <div className="bg-white overflow-hidden shadow rounded-lg">
              <div className="p-5">
                <div className="flex items-center">
                  <div className="flex-shrink-0">
                    <div className="w-8 h-8 bg-green-500 rounded-md flex items-center justify-center">
                      <svg className="w-5 h-5 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z" />
                      </svg>
                    </div>
                  </div>
                  <div className="ml-5 w-0 flex-1">
                    <dl>
                      <dt className="text-sm font-medium text-gray-500 truncate">Total Products</dt>
                      <dd className="text-lg font-medium text-gray-900">{stats.totalProducts}</dd>
                    </dl>
                  </div>
                </div>
              </div>
            </div>

            <div className="bg-white overflow-hidden shadow rounded-lg">
              <div className="p-5">
                <div className="flex items-center">
                  <div className="flex-shrink-0">
                    <div className="w-8 h-8 bg-yellow-500 rounded-md flex items-center justify-center">
                      <svg className="w-5 h-5 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1" />
                      </svg>
                    </div>
                  </div>
                  <div className="ml-5 w-0 flex-1">
                    <dl>
                      <dt className="text-sm font-medium text-gray-500 truncate">Inventory Value</dt>
                      <dd className="text-lg font-medium text-gray-900">৳{stats.totalInventoryValue.toLocaleString()}</dd>
                    </dl>
                  </div>
                </div>
              </div>
            </div>
          </div>

          {/* Chart */}
          <div className="bg-white shadow rounded-lg p-6 mb-6">
            <h2 className="text-lg font-medium text-gray-900 mb-4">Weekly Delivery Overview</h2>
            <div className="h-80">
              <ResponsiveContainer width="100%" height="100%">
                <BarChart data={stats.weeklyData}>
                  <CartesianGrid strokeDasharray="3 3" />
                  <XAxis dataKey="name" />
                  <YAxis />
                  <Tooltip />
                  <Bar dataKey="deliveries" fill="#3B82F6" name="Deliveries" />
                  <Bar dataKey="returns" fill="#EF4444" name="Returns" />
                </BarChart>
              </ResponsiveContainer>
            </div>
          </div>

          {/* Two Column Layout */}
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6 mb-6">
            {/* Top Products by Value */}
            <div className="bg-white shadow rounded-lg">
              <div className="px-4 py-5 sm:p-6">
                <h3 className="text-lg leading-6 font-medium text-gray-900 mb-4">Top Products by Inventory Value</h3>
                {stats.topProducts.length > 0 ? (
                  <div className="space-y-3">
                    {stats.topProducts.map((product, index) => (
                      <div key={index} className="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
                        <div>
                          <p className="text-sm font-medium text-gray-900">{product.name}</p>
                          <p className="text-xs text-gray-500">{product.currentInventory} units</p>
                        </div>
                        <div className="text-right">
                          <p className="text-sm font-medium text-gray-900">৳{product.totalCost.toLocaleString()}</p>
                        </div>
                      </div>
                    ))}
                  </div>
                ) : (
                  <p className="text-gray-500">No inventory data available</p>
                )}
              </div>
            </div>

            {/* Recent Deliveries */}
            <div className="bg-white shadow rounded-lg">
              <div className="px-4 py-5 sm:p-6">
                <h3 className="text-lg leading-6 font-medium text-gray-900 mb-4">Recent Deliveries</h3>
                {stats.recentDeliveries.length > 0 ? (
                  <div className="space-y-3">
                    {stats.recentDeliveries.map((delivery, index) => (
                      <div key={index} className="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
                        <div>
                          <p className="text-sm font-medium text-gray-900">
                            {format(new Date(delivery.date), 'MMM dd, yyyy')}
                          </p>
                          <p className="text-xs text-gray-500">Pickup: {delivery.total_pickup}</p>
                        </div>
                        <div className="text-right">
                          <p className="text-sm font-medium text-green-600">Delivered: {delivery.total_delivered}</p>
                        </div>
                      </div>
                    ))}
                  </div>
                ) : (
                  <p className="text-gray-500">No deliveries recorded yet</p>
                )}
              </div>
            </div>
          </div>

          {/* Quick Actions */}
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
            <Link href="/deliveries" className="bg-white overflow-hidden shadow rounded-lg hover:shadow-md transition-shadow">
              <div className="p-6">
                <div className="flex items-center">
                  <div className="flex-shrink-0">
                    <div className="w-10 h-10 bg-blue-500 rounded-md flex items-center justify-center">
                      <svg className="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M20 7l-8-4-8 4m16 0l-8 4m8-4v10l-8 4m0-10L4 7m8 4v10M4 7v10l8 4" />
                      </svg>
                    </div>
                  </div>
                  <div className="ml-4">
                    <h3 className="text-lg font-medium text-gray-900">Daily Deliveries</h3>
                    <p className="text-sm text-gray-500">Track daily pickups and deliveries</p>
                  </div>
                </div>
              </div>
            </Link>

            <Link href="/returns" className="bg-white overflow-hidden shadow rounded-lg hover:shadow-md transition-shadow">
              <div className="p-6">
                <div className="flex items-center">
                  <div className="flex-shrink-0">
                    <div className="w-10 h-10 bg-red-500 rounded-md flex items-center justify-center">
                      <svg className="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M16 15v-1a4 4 0 00-4-4H8m0 0l3 3m-3-3l3-3m9 14V5a2 2 0 00-2-2H6a2 2 0 00-2 2v16l4-2 4 2 4-2 4 2z" />
                      </svg>
                    </div>
                  </div>
                  <div className="ml-4">
                    <h3 className="text-lg font-medium text-gray-900">Returns</h3>
                    <p className="text-sm text-gray-500">Manage product returns</p>
                  </div>
                </div>
              </div>
            </Link>

            <Link href="/products" className="bg-white overflow-hidden shadow rounded-lg hover:shadow-md transition-shadow">
              <div className="p-6">
                <div className="flex items-center">
                  <div className="flex-shrink-0">
                    <div className="w-10 h-10 bg-green-500 rounded-md flex items-center justify-center">
                      <svg className="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z" />
                      </svg>
                    </div>
                  </div>
                  <div className="ml-4">
                    <h3 className="text-lg font-medium text-gray-900">Inventory</h3>
                    <p className="text-sm text-gray-500">Manage product inventory</p>
                  </div>
                </div>
              </div>
            </Link>

            <Link href="/expenses" className="bg-white overflow-hidden shadow rounded-lg hover:shadow-md transition-shadow">
              <div className="p-6">
                <div className="flex items-center">
                  <div className="flex-shrink-0">
                    <div className="w-10 h-10 bg-purple-500 rounded-md flex items-center justify-center">
                      <svg className="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1" />
                      </svg>
                    </div>
                  </div>
                  <div className="ml-4">
                    <h3 className="text-lg font-medium text-gray-900">Expenses</h3>
                    <p className="text-sm text-gray-500">Track business expenses</p>
                  </div>
                </div>
              </div>
            </Link>
          </div>
        </div>
      </div>
    </div>
  )
}
