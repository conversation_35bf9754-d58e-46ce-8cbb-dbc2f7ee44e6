'use client'

import { useEffect, useState } from 'react'
import Navigation from '@/components/Navigation'
import { supabase, isSupabaseConfigured, type Delivery, type Product } from '@/lib/supabase'
import { format } from 'date-fns'

interface DashboardStats {
  totalDeliveries: number
  totalPickups: number
  totalDelivered: number
  totalReturned: number
  successRate: number
  totalProducts: number
}

export default function Home() {
  const [stats, setStats] = useState<DashboardStats>({
    totalDeliveries: 0,
    totalPickups: 0,
    totalDelivered: 0,
    totalReturned: 0,
    successRate: 0,
    totalProducts: 0
  })
  const [recentDeliveries, setRecentDeliveries] = useState<Delivery[]>([])
  const [loading, setLoading] = useState(true)

  useEffect(() => {
    fetchDashboardData()
  }, [])

  const fetchDashboardData = async () => {
    if (!isSupabaseConfigured) {
      setLoading(false)
      return
    }

    try {
      // Fetch delivery statistics
      const { data: deliveries } = await supabase
        .from('deliveries')
        .select('*')
        .order('date', { ascending: false })

      // Fetch product count
      const { data: products } = await supabase
        .from('products')
        .select('id')

      if (deliveries) {
        const totalPickups = deliveries.reduce((sum, d) => sum + d.pickup_count, 0)
        const totalDelivered = deliveries.reduce((sum, d) => sum + d.delivered_count, 0)
        const totalReturned = deliveries.reduce((sum, d) => sum + d.returned_count, 0)
        const successRate = totalPickups > 0 ? (totalDelivered / totalPickups) * 100 : 0

        setStats({
          totalDeliveries: deliveries.length,
          totalPickups,
          totalDelivered,
          totalReturned,
          successRate,
          totalProducts: products?.length || 0
        })

        setRecentDeliveries(deliveries.slice(0, 5))
      }
    } catch (error) {
      console.error('Error fetching dashboard data:', error)
    } finally {
      setLoading(false)
    }
  }

  if (loading) {
    return (
      <div className="min-h-screen bg-gray-50">
        <Navigation />
        <div className="flex items-center justify-center h-96">
          <div className="text-lg">Loading dashboard...</div>
        </div>
      </div>
    )
  }

  if (!isSupabaseConfigured) {
    return (
      <div className="min-h-screen bg-gray-50">
        <Navigation />
        <div className="max-w-4xl mx-auto py-6 sm:px-6 lg:px-8">
          <div className="px-4 py-6 sm:px-0">
            <div className="bg-white shadow rounded-lg">
              <div className="px-4 py-5 sm:p-6">
                <h1 className="text-3xl font-bold text-gray-900 mb-6">Welcome to Inventory Management System!</h1>

                <div className="bg-blue-50 border border-blue-200 rounded-md p-6 mb-6">
                  <h2 className="text-xl font-semibold text-blue-900 mb-4">🚀 Setup Required</h2>
                  <p className="text-blue-800 mb-4">
                    To get started with your inventory management system, you need to configure your Supabase database.
                  </p>

                  <div className="space-y-4">
                    <div>
                      <h3 className="font-semibold text-blue-900">Step 1: Create Supabase Account</h3>
                      <p className="text-blue-700">Go to <a href="https://supabase.com" target="_blank" rel="noopener noreferrer" className="underline">supabase.com</a> and create a free account</p>
                    </div>

                    <div>
                      <h3 className="font-semibold text-blue-900">Step 2: Create Database</h3>
                      <p className="text-blue-700">Create a new project and run the SQL commands from <code className="bg-blue-100 px-1 py-0.5 rounded">database-setup.sql</code></p>
                    </div>

                    <div>
                      <h3 className="font-semibold text-blue-900">Step 3: Configure Environment</h3>
                      <p className="text-blue-700">Update your <code className="bg-blue-100 px-1 py-0.5 rounded">.env.local</code> file with your Supabase credentials</p>
                    </div>

                    <div>
                      <h3 className="font-semibold text-blue-900">Step 4: Restart Server</h3>
                      <p className="text-blue-700">Restart your development server to apply the changes</p>
                    </div>
                  </div>
                </div>

                <div className="bg-green-50 border border-green-200 rounded-md p-4">
                  <h3 className="font-semibold text-green-900 mb-2">✨ What you'll get:</h3>
                  <ul className="text-green-800 space-y-1">
                    <li>• Complete delivery tracking system</li>
                    <li>• Product inventory management</li>
                    <li>• Performance analytics and reports</li>
                    <li>• Data export and backup features</li>
                    <li>• Mobile-responsive design</li>
                  </ul>
                </div>

                <div className="mt-6">
                  <p className="text-gray-600">
                    Need help? Check the <strong>Settings</strong> page for detailed setup instructions, or refer to the <code className="bg-gray-100 px-1 py-0.5 rounded">README.md</code> file.
                  </p>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    )
  }

  return (
    <div className="min-h-screen bg-gray-50">
      <Navigation />

      <div className="max-w-7xl mx-auto py-6 sm:px-6 lg:px-8">
        <div className="px-4 py-6 sm:px-0">
          <h1 className="text-3xl font-bold text-gray-900 mb-8">Dashboard</h1>

          {/* Stats Grid */}
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
            <div className="bg-white overflow-hidden shadow rounded-lg">
              <div className="p-5">
                <div className="flex items-center">
                  <div className="flex-shrink-0">
                    <div className="w-8 h-8 bg-blue-500 rounded-md flex items-center justify-center">
                      <span className="text-white font-bold">D</span>
                    </div>
                  </div>
                  <div className="ml-5 w-0 flex-1">
                    <dl>
                      <dt className="text-sm font-medium text-gray-500 truncate">Total Deliveries</dt>
                      <dd className="text-lg font-medium text-gray-900">{stats.totalDeliveries}</dd>
                    </dl>
                  </div>
                </div>
              </div>
            </div>

            <div className="bg-white overflow-hidden shadow rounded-lg">
              <div className="p-5">
                <div className="flex items-center">
                  <div className="flex-shrink-0">
                    <div className="w-8 h-8 bg-yellow-500 rounded-md flex items-center justify-center">
                      <span className="text-white font-bold">P</span>
                    </div>
                  </div>
                  <div className="ml-5 w-0 flex-1">
                    <dl>
                      <dt className="text-sm font-medium text-gray-500 truncate">Total Pickups</dt>
                      <dd className="text-lg font-medium text-gray-900">{stats.totalPickups}</dd>
                    </dl>
                  </div>
                </div>
              </div>
            </div>

            <div className="bg-white overflow-hidden shadow rounded-lg">
              <div className="p-5">
                <div className="flex items-center">
                  <div className="flex-shrink-0">
                    <div className="w-8 h-8 bg-green-500 rounded-md flex items-center justify-center">
                      <span className="text-white font-bold">✓</span>
                    </div>
                  </div>
                  <div className="ml-5 w-0 flex-1">
                    <dl>
                      <dt className="text-sm font-medium text-gray-500 truncate">Delivered</dt>
                      <dd className="text-lg font-medium text-gray-900">{stats.totalDelivered}</dd>
                    </dl>
                  </div>
                </div>
              </div>
            </div>

            <div className="bg-white overflow-hidden shadow rounded-lg">
              <div className="p-5">
                <div className="flex items-center">
                  <div className="flex-shrink-0">
                    <div className="w-8 h-8 bg-red-500 rounded-md flex items-center justify-center">
                      <span className="text-white font-bold">↩</span>
                    </div>
                  </div>
                  <div className="ml-5 w-0 flex-1">
                    <dl>
                      <dt className="text-sm font-medium text-gray-500 truncate">Returned</dt>
                      <dd className="text-lg font-medium text-gray-900">{stats.totalReturned}</dd>
                    </dl>
                  </div>
                </div>
              </div>
            </div>
          </div>

          {/* Success Rate */}
          <div className="bg-white shadow rounded-lg mb-8">
            <div className="px-4 py-5 sm:p-6">
              <h3 className="text-lg leading-6 font-medium text-gray-900 mb-4">Delivery Success Rate</h3>
              <div className="flex items-center">
                <div className="flex-1">
                  <div className="bg-gray-200 rounded-full h-4">
                    <div
                      className="bg-green-500 h-4 rounded-full transition-all duration-300"
                      style={{ width: `${Math.min(stats.successRate, 100)}%` }}
                    ></div>
                  </div>
                </div>
                <div className="ml-4">
                  <span className="text-2xl font-bold text-gray-900">{stats.successRate.toFixed(1)}%</span>
                </div>
              </div>
            </div>
          </div>

          {/* Recent Deliveries */}
          <div className="bg-white shadow rounded-lg">
            <div className="px-4 py-5 sm:p-6">
              <h3 className="text-lg leading-6 font-medium text-gray-900 mb-4">Recent Deliveries</h3>
              {recentDeliveries.length > 0 ? (
                <div className="overflow-x-auto">
                  <table className="min-w-full divide-y divide-gray-200">
                    <thead className="bg-gray-50">
                      <tr>
                        <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Date</th>
                        <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Pickup</th>
                        <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Delivered</th>
                        <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Returned</th>
                        <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Success Rate</th>
                      </tr>
                    </thead>
                    <tbody className="bg-white divide-y divide-gray-200">
                      {recentDeliveries.map((delivery) => {
                        const successRate = delivery.pickup_count > 0
                          ? (delivery.delivered_count / delivery.pickup_count) * 100
                          : 0
                        return (
                          <tr key={delivery.id}>
                            <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                              {format(new Date(delivery.date), 'MMM dd, yyyy')}
                            </td>
                            <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">{delivery.pickup_count}</td>
                            <td className="px-6 py-4 whitespace-nowrap text-sm text-green-600">{delivery.delivered_count}</td>
                            <td className="px-6 py-4 whitespace-nowrap text-sm text-red-600">{delivery.returned_count}</td>
                            <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">{successRate.toFixed(1)}%</td>
                          </tr>
                        )
                      })}
                    </tbody>
                  </table>
                </div>
              ) : (
                <p className="text-gray-500">No deliveries recorded yet. <a href="/deliveries" className="text-blue-600 hover:text-blue-800">Create your first delivery</a></p>
              )}
            </div>
          </div>
        </div>
      </div>
    </div>
  )
}
