{"version": 3, "sources": [], "sections": [{"offset": {"line": 39, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/Projects/inventory-management-system/src/components/Navigation.tsx"], "sourcesContent": ["'use client'\n\nimport Link from 'next/link'\nimport { usePathname } from 'next/navigation'\nimport {\n  HomeIcon,\n  TruckIcon,\n  CubeIcon,\n  ChartBarIcon,\n  Cog6ToothIcon,\n  ArrowUturnLeftIcon,\n  CurrencyDollarIcon\n} from '@heroicons/react/24/outline'\n\nconst navigation = [\n  { name: 'Dashboard', href: '/', icon: HomeIcon },\n  { name: 'Daily Deliveries', href: '/deliveries', icon: TruckIcon },\n  { name: 'Returns', href: '/returns', icon: ArrowUturnLeftIcon },\n  { name: 'Inventory', href: '/products', icon: CubeIcon },\n  { name: 'Expenses', href: '/expenses', icon: CurrencyDollarIcon },\n  { name: 'Reports', href: '/reports', icon: ChartBarIcon },\n  { name: 'Settings', href: '/settings', icon: Cog6ToothIcon },\n]\n\nexport default function Navigation() {\n  const pathname = usePathname()\n\n  return (\n    <nav className=\"bg-gray-800\">\n      <div className=\"mx-auto max-w-7xl px-4 sm:px-6 lg:px-8\">\n        <div className=\"flex h-16 items-center justify-between\">\n          <div className=\"flex items-center\">\n            <div className=\"flex-shrink-0\">\n              <h1 className=\"text-white text-xl font-bold\">Cosmetics Inventory</h1>\n            </div>\n            <div className=\"hidden md:block\">\n              <div className=\"ml-10 flex items-baseline space-x-4\">\n                {navigation.map((item) => {\n                  const isActive = pathname === item.href\n                  return (\n                    <Link\n                      key={item.name}\n                      href={item.href}\n                      className={`${\n                        isActive\n                          ? 'bg-gray-900 text-white'\n                          : 'text-gray-300 hover:bg-gray-700 hover:text-white'\n                      } rounded-md px-3 py-2 text-sm font-medium flex items-center space-x-2`}\n                    >\n                      <item.icon className=\"h-5 w-5\" />\n                      <span>{item.name}</span>\n                    </Link>\n                  )\n                })}\n              </div>\n            </div>\n          </div>\n        </div>\n      </div>\n    </nav>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAJA;;;;;AAcA,MAAM,aAAa;IACjB;QAAE,MAAM;QAAa,MAAM;QAAK,MAAM,+MAAA,CAAA,WAAQ;IAAC;IAC/C;QAAE,MAAM;QAAoB,MAAM;QAAe,MAAM,iNAAA,CAAA,YAAS;IAAC;IACjE;QAAE,MAAM;QAAW,MAAM;QAAY,MAAM,mOAAA,CAAA,qBAAkB;IAAC;IAC9D;QAAE,MAAM;QAAa,MAAM;QAAa,MAAM,+MAAA,CAAA,WAAQ;IAAC;IACvD;QAAE,MAAM;QAAY,MAAM;QAAa,MAAM,mOAAA,CAAA,qBAAkB;IAAC;IAChE;QAAE,MAAM;QAAW,MAAM;QAAY,MAAM,uNAAA,CAAA,eAAY;IAAC;IACxD;QAAE,MAAM;QAAY,MAAM;QAAa,MAAM,yNAAA,CAAA,gBAAa;IAAC;CAC5D;AAEc,SAAS;IACtB,MAAM,WAAW,CAAA,GAAA,kIAAA,CAAA,cAAW,AAAD;IAE3B,qBACE,8OAAC;QAAI,WAAU;kBACb,cAAA,8OAAC;YAAI,WAAU;sBACb,cAAA,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAI,WAAU;sCACb,cAAA,8OAAC;gCAAG,WAAU;0CAA+B;;;;;;;;;;;sCAE/C,8OAAC;4BAAI,WAAU;sCACb,cAAA,8OAAC;gCAAI,WAAU;0CACZ,WAAW,GAAG,CAAC,CAAC;oCACf,MAAM,WAAW,aAAa,KAAK,IAAI;oCACvC,qBACE,8OAAC,4JAAA,CAAA,UAAI;wCAEH,MAAM,KAAK,IAAI;wCACf,WAAW,GACT,WACI,2BACA,mDACL,qEAAqE,CAAC;;0DAEvE,8OAAC,KAAK,IAAI;gDAAC,WAAU;;;;;;0DACrB,8OAAC;0DAAM,KAAK,IAAI;;;;;;;uCATX,KAAK,IAAI;;;;;gCAYpB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAQhB", "debugId": null}}, {"offset": {"line": 277, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/Projects/inventory-management-system/src/lib/supabase.ts"], "sourcesContent": ["import { createClient } from '@supabase/supabase-js'\n\nconst supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL || 'https://placeholder.supabase.co'\nconst supabaseAnonKey = process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY || 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InBsYWNlaG9sZGVyIiwicm9sZSI6ImFub24iLCJpYXQiOjE2NDUxOTI4MDAsImV4cCI6MTk2MDc2ODgwMH0.placeholder'\n\n// Check if environment variables are properly configured\nexport const isSupabaseConfigured =\n  supabaseUrl !== 'https://placeholder.supabase.co' &&\n  supabaseUrl !== 'your_supabase_url_here' &&\n  supabaseAnonKey !== 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InBsYWNlaG9sZGVyIiwicm9sZSI6ImFub24iLCJpYXQiOjE2NDUxOTI4MDAsImV4cCI6MTk2MDc2ODgwMH0.placeholder' &&\n  supabaseAnonKey !== 'your_supabase_anon_key_here'\n\n// Only create client if properly configured, otherwise use a mock\nexport const supabase = isSupabaseConfigured\n  ? createClient(supabaseUrl, supabaseAnonKey)\n  : createClient('https://placeholder.supabase.co', 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InBsYWNlaG9sZGVyIiwicm9sZSI6ImFub24iLCJpYXQiOjE2NDUxOTI4MDAsImV4cCI6MTk2MDc2ODgwMH0.placeholder')\n\n// Database Types\nexport interface Product {\n  id: string\n  sku: string\n  name: string\n  category?: string\n  price?: number\n  description?: string\n  created_at: string\n  updated_at: string\n}\n\nexport interface Delivery {\n  id: string\n  date: string\n  pickup_count: number\n  delivered_count: number\n  returned_count: number\n  courier?: string\n  notes?: string\n  created_at: string\n  updated_at: string\n}\n\nexport interface DeliveryItem {\n  id: string\n  delivery_id: string\n  product_id: string\n  quantity: number\n  product?: Product\n}\n\nexport interface DeliveryWithItems extends Delivery {\n  delivery_items: DeliveryItem[]\n}\n"], "names": [], "mappings": ";;;;AAAA;;AAEA,MAAM,cAAc,8DAAwC;AAC5D,MAAM,kBAAkB,mEAA6C;AAG9D,MAAM,uBACX,gBAAgB,qCAChB,gBAAgB,4BAChB,oBAAoB,0KACpB,oBAAoB;AAGf,MAAM,WAAW,6EAEpB,CAAA,GAAA,uLAAA,CAAA,eAAY,AAAD,EAAE,mCAAmC", "debugId": null}}, {"offset": {"line": 293, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/Projects/inventory-management-system/src/app/page.tsx"], "sourcesContent": ["'use client'\n\nimport { useEffect, useState } from 'react'\nimport Link from 'next/link'\nimport Navigation from '@/components/Navigation'\nimport { supabase, isSupabaseConfigured } from '@/lib/supabase'\nimport { format } from 'date-fns'\nimport { BarChart, Bar, XAxis, YAxis, CartesianGrid, Tooltip, ResponsiveContainer } from 'recharts'\n\ninterface DashboardStats {\n  totalInventoryValue: number\n  totalProducts: number\n  todayDeliveries: number\n  todayReturns: number\n  weeklyData: Array<{\n    name: string\n    deliveries: number\n    returns: number\n  }>\n  topProducts: Array<{\n    name: string\n    currentInventory: number\n    totalCost: number\n  }>\n  recentDeliveries: Array<{\n    date: string\n    total_pickup: number\n    total_delivered: number\n  }>\n}\n\nexport default function Home() {\n  const [stats, setStats] = useState<DashboardStats>({\n    totalInventoryValue: 0,\n    totalProducts: 0,\n    todayDeliveries: 0,\n    todayReturns: 0,\n    weeklyData: [],\n    topProducts: [],\n    recentDeliveries: []\n  })\n  const [loading, setLoading] = useState(true)\n\n  useEffect(() => {\n    fetchDashboardData()\n  }, [])\n\n  const fetchDashboardData = async () => {\n    if (!isSupabaseConfigured) {\n      setLoading(false)\n      return\n    }\n\n    try {\n      // Get total inventory value and product count\n      const { data: inventoryData } = await supabase\n        .from('inventory')\n        .select(`\n          current_inventory,\n          total_cost,\n          products (name, cost_per_unit)\n        `)\n\n      // Get today's deliveries\n      const today = new Date().toISOString().split('T')[0]\n      const { data: todayDeliveryData } = await supabase\n        .from('daily_deliveries')\n        .select('total_delivered')\n        .eq('date', today)\n        .single()\n\n      // Get today's returns\n      const { data: todayReturnData } = await supabase\n        .from('returns')\n        .select('return_count')\n        .eq('date', today)\n        .single()\n\n      // Get recent deliveries (last 7 days)\n      const weekAgo = new Date()\n      weekAgo.setDate(weekAgo.getDate() - 7)\n      const { data: recentDeliveries } = await supabase\n        .from('daily_deliveries')\n        .select('date, total_pickup, total_delivered')\n        .gte('date', weekAgo.toISOString().split('T')[0])\n        .order('date', { ascending: false })\n        .limit(5)\n\n      // Get weekly delivery data for chart\n      const { data: weeklyDeliveries } = await supabase\n        .from('daily_deliveries')\n        .select('date, total_delivered')\n        .gte('date', weekAgo.toISOString().split('T')[0])\n        .order('date', { ascending: true })\n\n      const { data: weeklyReturns } = await supabase\n        .from('returns')\n        .select('date, return_count')\n        .gte('date', weekAgo.toISOString().split('T')[0])\n        .order('date', { ascending: true })\n\n      // Process data\n      const totalInventoryValue = inventoryData?.reduce((sum, item) => sum + (item.total_cost || 0), 0) || 0\n      const totalProducts = inventoryData?.length || 0\n\n      // Create weekly chart data\n      const weeklyData = []\n      const days = ['Sun', 'Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat']\n      for (let i = 0; i < 7; i++) {\n        const date = new Date()\n        date.setDate(date.getDate() - (6 - i))\n        const dateStr = date.toISOString().split('T')[0]\n        const dayName = days[date.getDay()]\n\n        const deliveryData = weeklyDeliveries?.find(d => d.date === dateStr)\n        const returnData = weeklyReturns?.find(r => r.date === dateStr)\n\n        weeklyData.push({\n          name: dayName,\n          deliveries: deliveryData?.total_delivered || 0,\n          returns: returnData?.return_count || 0\n        })\n      }\n\n      // Get top products by inventory value\n      const topProducts = inventoryData\n        ?.filter(item => item.current_inventory > 0)\n        ?.sort((a, b) => (b.total_cost || 0) - (a.total_cost || 0))\n        ?.slice(0, 5)\n        ?.map(item => ({\n          name: item.products?.name || 'Unknown',\n          currentInventory: item.current_inventory,\n          totalCost: item.total_cost || 0\n        })) || []\n\n      setStats({\n        totalInventoryValue,\n        totalProducts,\n        todayDeliveries: todayDeliveryData?.total_delivered || 0,\n        todayReturns: todayReturnData?.return_count || 0,\n        weeklyData,\n        topProducts,\n        recentDeliveries: recentDeliveries || []\n      })\n    } catch (error) {\n      console.error('Error fetching dashboard data:', error)\n    } finally {\n      setLoading(false)\n    }\n  }\n\n  if (loading) {\n    return (\n      <div className=\"min-h-screen bg-gray-50\">\n        <Navigation />\n        <div className=\"flex items-center justify-center h-96\">\n          <div className=\"animate-spin rounded-full h-32 w-32 border-b-2 border-blue-500\"></div>\n        </div>\n      </div>\n    )\n  }\n\n  if (!isSupabaseConfigured) {\n    return (\n      <div className=\"min-h-screen bg-gray-50\">\n        <Navigation />\n        <div className=\"max-w-4xl mx-auto py-6 sm:px-6 lg:px-8\">\n          <div className=\"px-4 py-6 sm:px-0\">\n            <div className=\"bg-white shadow rounded-lg\">\n              <div className=\"px-4 py-5 sm:p-6\">\n                <h1 className=\"text-3xl font-bold text-gray-900 mb-6\">Welcome to Cosmetics Inventory Management!</h1>\n\n                <div className=\"bg-blue-50 border border-blue-200 rounded-md p-6 mb-6\">\n                  <h2 className=\"text-xl font-semibold text-blue-900 mb-4\">🚀 Setup Required</h2>\n                  <p className=\"text-blue-800 mb-4\">\n                    To get started with your cosmetics inventory management system, you need to configure your Supabase database.\n                  </p>\n\n                  <div className=\"space-y-4\">\n                    <div>\n                      <h3 className=\"font-semibold text-blue-900\">Step 1: Create Supabase Account</h3>\n                      <p className=\"text-blue-700\">Go to <a href=\"https://supabase.com\" target=\"_blank\" rel=\"noopener noreferrer\" className=\"underline\">supabase.com</a> and create a free account</p>\n                    </div>\n\n                    <div>\n                      <h3 className=\"font-semibold text-blue-900\">Step 2: Create Database</h3>\n                      <p className=\"text-blue-700\">Create a new project and run the SQL commands from <code className=\"bg-blue-100 px-1 py-0.5 rounded\">database-setup.sql</code></p>\n                    </div>\n\n                    <div>\n                      <h3 className=\"font-semibold text-blue-900\">Step 3: Configure Environment</h3>\n                      <p className=\"text-blue-700\">Update your <code className=\"bg-blue-100 px-1 py-0.5 rounded\">.env.local</code> file with your Supabase credentials</p>\n                    </div>\n\n                    <div>\n                      <h3 className=\"font-semibold text-blue-900\">Step 4: Restart Server</h3>\n                      <p className=\"text-blue-700\">Restart your development server to apply the changes</p>\n                    </div>\n                  </div>\n                </div>\n\n                <div className=\"bg-green-50 border border-green-200 rounded-md p-4\">\n                  <h3 className=\"font-semibold text-green-900 mb-2\">✨ What you'll get:</h3>\n                  <ul className=\"text-green-800 space-y-1\">\n                    <li>• Daily delivery tracking with product quantities</li>\n                    <li>• Returns management system</li>\n                    <li>• Inventory tracking with cost calculations</li>\n                    <li>• Monthly purchase history</li>\n                    <li>• Expense management</li>\n                    <li>• Financial reports and analytics</li>\n                  </ul>\n                </div>\n\n                <div className=\"mt-6\">\n                  <p className=\"text-gray-600\">\n                    Need help? Check the <strong>Settings</strong> page for detailed setup instructions, or refer to the <code className=\"bg-gray-100 px-1 py-0.5 rounded\">README.md</code> file.\n                  </p>\n                </div>\n              </div>\n            </div>\n          </div>\n        </div>\n      </div>\n    )\n  }\n\n  return (\n    <div className=\"min-h-screen bg-gray-50\">\n      <Navigation />\n\n      <div className=\"max-w-7xl mx-auto py-6 sm:px-6 lg:px-8\">\n        <div className=\"px-4 py-6 sm:px-0\">\n          {/* Header */}\n          <div className=\"bg-white shadow rounded-lg p-6 mb-6\">\n            <h1 className=\"text-3xl font-bold text-gray-900\">Cosmetics Inventory Management</h1>\n            <p className=\"mt-2 text-gray-600\">Track your deliveries, returns, and inventory in real-time</p>\n          </div>\n\n          {/* Quick Stats */}\n          <div className=\"grid grid-cols-1 md:grid-cols-4 gap-6 mb-6\">\n            <div className=\"bg-white overflow-hidden shadow rounded-lg\">\n              <div className=\"p-5\">\n                <div className=\"flex items-center\">\n                  <div className=\"flex-shrink-0\">\n                    <div className=\"w-8 h-8 bg-blue-500 rounded-md flex items-center justify-center\">\n                      <svg className=\"w-5 h-5 text-white\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                        <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M20 7l-8-4-8 4m16 0l-8 4m8-4v10l-8 4m0-10L4 7m8 4v10M4 7v10l8 4\" />\n                      </svg>\n                    </div>\n                  </div>\n                  <div className=\"ml-5 w-0 flex-1\">\n                    <dl>\n                      <dt className=\"text-sm font-medium text-gray-500 truncate\">Today's Deliveries</dt>\n                      <dd className=\"text-lg font-medium text-gray-900\">{stats.todayDeliveries}</dd>\n                    </dl>\n                  </div>\n                </div>\n              </div>\n            </div>\n\n            <div className=\"bg-white overflow-hidden shadow rounded-lg\">\n              <div className=\"p-5\">\n                <div className=\"flex items-center\">\n                  <div className=\"flex-shrink-0\">\n                    <div className=\"w-8 h-8 bg-red-500 rounded-md flex items-center justify-center\">\n                      <svg className=\"w-5 h-5 text-white\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                        <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M16 15v-1a4 4 0 00-4-4H8m0 0l3 3m-3-3l3-3m9 14V5a2 2 0 00-2-2H6a2 2 0 00-2 2v16l4-2 4 2 4-2 4 2z\" />\n                      </svg>\n                    </div>\n                  </div>\n                  <div className=\"ml-5 w-0 flex-1\">\n                    <dl>\n                      <dt className=\"text-sm font-medium text-gray-500 truncate\">Today's Returns</dt>\n                      <dd className=\"text-lg font-medium text-gray-900\">{stats.todayReturns}</dd>\n                    </dl>\n                  </div>\n                </div>\n              </div>\n            </div>\n\n            <div className=\"bg-white overflow-hidden shadow rounded-lg\">\n              <div className=\"p-5\">\n                <div className=\"flex items-center\">\n                  <div className=\"flex-shrink-0\">\n                    <div className=\"w-8 h-8 bg-green-500 rounded-md flex items-center justify-center\">\n                      <svg className=\"w-5 h-5 text-white\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                        <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z\" />\n                      </svg>\n                    </div>\n                  </div>\n                  <div className=\"ml-5 w-0 flex-1\">\n                    <dl>\n                      <dt className=\"text-sm font-medium text-gray-500 truncate\">Total Products</dt>\n                      <dd className=\"text-lg font-medium text-gray-900\">{stats.totalProducts}</dd>\n                    </dl>\n                  </div>\n                </div>\n              </div>\n            </div>\n\n            <div className=\"bg-white overflow-hidden shadow rounded-lg\">\n              <div className=\"p-5\">\n                <div className=\"flex items-center\">\n                  <div className=\"flex-shrink-0\">\n                    <div className=\"w-8 h-8 bg-yellow-500 rounded-md flex items-center justify-center\">\n                      <svg className=\"w-5 h-5 text-white\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                        <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1\" />\n                      </svg>\n                    </div>\n                  </div>\n                  <div className=\"ml-5 w-0 flex-1\">\n                    <dl>\n                      <dt className=\"text-sm font-medium text-gray-500 truncate\">Inventory Value</dt>\n                      <dd className=\"text-lg font-medium text-gray-900\">৳{stats.totalInventoryValue.toLocaleString()}</dd>\n                    </dl>\n                  </div>\n                </div>\n              </div>\n            </div>\n          </div>\n\n          {/* Chart */}\n          <div className=\"bg-white shadow rounded-lg p-6 mb-6\">\n            <h2 className=\"text-lg font-medium text-gray-900 mb-4\">Weekly Delivery Overview</h2>\n            <div className=\"h-80\">\n              <ResponsiveContainer width=\"100%\" height=\"100%\">\n                <BarChart data={stats.weeklyData}>\n                  <CartesianGrid strokeDasharray=\"3 3\" />\n                  <XAxis dataKey=\"name\" />\n                  <YAxis />\n                  <Tooltip />\n                  <Bar dataKey=\"deliveries\" fill=\"#3B82F6\" name=\"Deliveries\" />\n                  <Bar dataKey=\"returns\" fill=\"#EF4444\" name=\"Returns\" />\n                </BarChart>\n              </ResponsiveContainer>\n            </div>\n          </div>\n\n          {/* Two Column Layout */}\n          <div className=\"grid grid-cols-1 lg:grid-cols-2 gap-6 mb-6\">\n            {/* Top Products by Value */}\n            <div className=\"bg-white shadow rounded-lg\">\n              <div className=\"px-4 py-5 sm:p-6\">\n                <h3 className=\"text-lg leading-6 font-medium text-gray-900 mb-4\">Top Products by Inventory Value</h3>\n                {stats.topProducts.length > 0 ? (\n                  <div className=\"space-y-3\">\n                    {stats.topProducts.map((product, index) => (\n                      <div key={index} className=\"flex items-center justify-between p-3 bg-gray-50 rounded-lg\">\n                        <div>\n                          <p className=\"text-sm font-medium text-gray-900\">{product.name}</p>\n                          <p className=\"text-xs text-gray-500\">{product.currentInventory} units</p>\n                        </div>\n                        <div className=\"text-right\">\n                          <p className=\"text-sm font-medium text-gray-900\">৳{product.totalCost.toLocaleString()}</p>\n                        </div>\n                      </div>\n                    ))}\n                  </div>\n                ) : (\n                  <p className=\"text-gray-500\">No inventory data available</p>\n                )}\n              </div>\n            </div>\n\n            {/* Recent Deliveries */}\n            <div className=\"bg-white shadow rounded-lg\">\n              <div className=\"px-4 py-5 sm:p-6\">\n                <h3 className=\"text-lg leading-6 font-medium text-gray-900 mb-4\">Recent Deliveries</h3>\n                {stats.recentDeliveries.length > 0 ? (\n                  <div className=\"space-y-3\">\n                    {stats.recentDeliveries.map((delivery, index) => (\n                      <div key={index} className=\"flex items-center justify-between p-3 bg-gray-50 rounded-lg\">\n                        <div>\n                          <p className=\"text-sm font-medium text-gray-900\">\n                            {format(new Date(delivery.date), 'MMM dd, yyyy')}\n                          </p>\n                          <p className=\"text-xs text-gray-500\">Pickup: {delivery.total_pickup}</p>\n                        </div>\n                        <div className=\"text-right\">\n                          <p className=\"text-sm font-medium text-green-600\">Delivered: {delivery.total_delivered}</p>\n                        </div>\n                      </div>\n                    ))}\n                  </div>\n                ) : (\n                  <p className=\"text-gray-500\">No deliveries recorded yet</p>\n                )}\n              </div>\n            </div>\n          </div>\n\n          {/* Quick Actions */}\n          <div className=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6\">\n            <Link href=\"/deliveries\" className=\"bg-white overflow-hidden shadow rounded-lg hover:shadow-md transition-shadow\">\n              <div className=\"p-6\">\n                <div className=\"flex items-center\">\n                  <div className=\"flex-shrink-0\">\n                    <div className=\"w-10 h-10 bg-blue-500 rounded-md flex items-center justify-center\">\n                      <svg className=\"w-6 h-6 text-white\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                        <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M20 7l-8-4-8 4m16 0l-8 4m8-4v10l-8 4m0-10L4 7m8 4v10M4 7v10l8 4\" />\n                      </svg>\n                    </div>\n                  </div>\n                  <div className=\"ml-4\">\n                    <h3 className=\"text-lg font-medium text-gray-900\">Daily Deliveries</h3>\n                    <p className=\"text-sm text-gray-500\">Track daily pickups and deliveries</p>\n                  </div>\n                </div>\n              </div>\n            </Link>\n\n            <Link href=\"/returns\" className=\"bg-white overflow-hidden shadow rounded-lg hover:shadow-md transition-shadow\">\n              <div className=\"p-6\">\n                <div className=\"flex items-center\">\n                  <div className=\"flex-shrink-0\">\n                    <div className=\"w-10 h-10 bg-red-500 rounded-md flex items-center justify-center\">\n                      <svg className=\"w-6 h-6 text-white\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                        <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M16 15v-1a4 4 0 00-4-4H8m0 0l3 3m-3-3l3-3m9 14V5a2 2 0 00-2-2H6a2 2 0 00-2 2v16l4-2 4 2 4-2 4 2z\" />\n                      </svg>\n                    </div>\n                  </div>\n                  <div className=\"ml-4\">\n                    <h3 className=\"text-lg font-medium text-gray-900\">Returns</h3>\n                    <p className=\"text-sm text-gray-500\">Manage product returns</p>\n                  </div>\n                </div>\n              </div>\n            </Link>\n\n            <Link href=\"/products\" className=\"bg-white overflow-hidden shadow rounded-lg hover:shadow-md transition-shadow\">\n              <div className=\"p-6\">\n                <div className=\"flex items-center\">\n                  <div className=\"flex-shrink-0\">\n                    <div className=\"w-10 h-10 bg-green-500 rounded-md flex items-center justify-center\">\n                      <svg className=\"w-6 h-6 text-white\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                        <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z\" />\n                      </svg>\n                    </div>\n                  </div>\n                  <div className=\"ml-4\">\n                    <h3 className=\"text-lg font-medium text-gray-900\">Inventory</h3>\n                    <p className=\"text-sm text-gray-500\">Manage product inventory</p>\n                  </div>\n                </div>\n              </div>\n            </Link>\n\n            <Link href=\"/expenses\" className=\"bg-white overflow-hidden shadow rounded-lg hover:shadow-md transition-shadow\">\n              <div className=\"p-6\">\n                <div className=\"flex items-center\">\n                  <div className=\"flex-shrink-0\">\n                    <div className=\"w-10 h-10 bg-purple-500 rounded-md flex items-center justify-center\">\n                      <svg className=\"w-6 h-6 text-white\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                        <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1\" />\n                      </svg>\n                    </div>\n                  </div>\n                  <div className=\"ml-4\">\n                    <h3 className=\"text-lg font-medium text-gray-900\">Expenses</h3>\n                    <p className=\"text-sm text-gray-500\">Track business expenses</p>\n                  </div>\n                </div>\n              </div>\n            </Link>\n          </div>\n        </div>\n      </div>\n    </div>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAPA;;;;;;;;AA+Be,SAAS;IACtB,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAkB;QACjD,qBAAqB;QACrB,eAAe;QACf,iBAAiB;QACjB,cAAc;QACd,YAAY,EAAE;QACd,aAAa,EAAE;QACf,kBAAkB,EAAE;IACtB;IACA,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAEvC,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR;IACF,GAAG,EAAE;IAEL,MAAM,qBAAqB;QACzB,IAAI,CAAC,sHAAA,CAAA,uBAAoB,EAAE;YACzB,WAAW;YACX;QACF;QAEA,IAAI;YACF,8CAA8C;YAC9C,MAAM,EAAE,MAAM,aAAa,EAAE,GAAG,MAAM,sHAAA,CAAA,WAAQ,CAC3C,IAAI,CAAC,aACL,MAAM,CAAC,CAAC;;;;QAIT,CAAC;YAEH,yBAAyB;YACzB,MAAM,QAAQ,IAAI,OAAO,WAAW,GAAG,KAAK,CAAC,IAAI,CAAC,EAAE;YACpD,MAAM,EAAE,MAAM,iBAAiB,EAAE,GAAG,MAAM,sHAAA,CAAA,WAAQ,CAC/C,IAAI,CAAC,oBACL,MAAM,CAAC,mBACP,EAAE,CAAC,QAAQ,OACX,MAAM;YAET,sBAAsB;YACtB,MAAM,EAAE,MAAM,eAAe,EAAE,GAAG,MAAM,sHAAA,CAAA,WAAQ,CAC7C,IAAI,CAAC,WACL,MAAM,CAAC,gBACP,EAAE,CAAC,QAAQ,OACX,MAAM;YAET,sCAAsC;YACtC,MAAM,UAAU,IAAI;YACpB,QAAQ,OAAO,CAAC,QAAQ,OAAO,KAAK;YACpC,MAAM,EAAE,MAAM,gBAAgB,EAAE,GAAG,MAAM,sHAAA,CAAA,WAAQ,CAC9C,IAAI,CAAC,oBACL,MAAM,CAAC,uCACP,GAAG,CAAC,QAAQ,QAAQ,WAAW,GAAG,KAAK,CAAC,IAAI,CAAC,EAAE,EAC/C,KAAK,CAAC,QAAQ;gBAAE,WAAW;YAAM,GACjC,KAAK,CAAC;YAET,qCAAqC;YACrC,MAAM,EAAE,MAAM,gBAAgB,EAAE,GAAG,MAAM,sHAAA,CAAA,WAAQ,CAC9C,IAAI,CAAC,oBACL,MAAM,CAAC,yBACP,GAAG,CAAC,QAAQ,QAAQ,WAAW,GAAG,KAAK,CAAC,IAAI,CAAC,EAAE,EAC/C,KAAK,CAAC,QAAQ;gBAAE,WAAW;YAAK;YAEnC,MAAM,EAAE,MAAM,aAAa,EAAE,GAAG,MAAM,sHAAA,CAAA,WAAQ,CAC3C,IAAI,CAAC,WACL,MAAM,CAAC,sBACP,GAAG,CAAC,QAAQ,QAAQ,WAAW,GAAG,KAAK,CAAC,IAAI,CAAC,EAAE,EAC/C,KAAK,CAAC,QAAQ;gBAAE,WAAW;YAAK;YAEnC,eAAe;YACf,MAAM,sBAAsB,eAAe,OAAO,CAAC,KAAK,OAAS,MAAM,CAAC,KAAK,UAAU,IAAI,CAAC,GAAG,MAAM;YACrG,MAAM,gBAAgB,eAAe,UAAU;YAE/C,2BAA2B;YAC3B,MAAM,aAAa,EAAE;YACrB,MAAM,OAAO;gBAAC;gBAAO;gBAAO;gBAAO;gBAAO;gBAAO;gBAAO;aAAM;YAC9D,IAAK,IAAI,IAAI,GAAG,IAAI,GAAG,IAAK;gBAC1B,MAAM,OAAO,IAAI;gBACjB,KAAK,OAAO,CAAC,KAAK,OAAO,KAAK,CAAC,IAAI,CAAC;gBACpC,MAAM,UAAU,KAAK,WAAW,GAAG,KAAK,CAAC,IAAI,CAAC,EAAE;gBAChD,MAAM,UAAU,IAAI,CAAC,KAAK,MAAM,GAAG;gBAEnC,MAAM,eAAe,kBAAkB,KAAK,CAAA,IAAK,EAAE,IAAI,KAAK;gBAC5D,MAAM,aAAa,eAAe,KAAK,CAAA,IAAK,EAAE,IAAI,KAAK;gBAEvD,WAAW,IAAI,CAAC;oBACd,MAAM;oBACN,YAAY,cAAc,mBAAmB;oBAC7C,SAAS,YAAY,gBAAgB;gBACvC;YACF;YAEA,sCAAsC;YACtC,MAAM,cAAc,eAChB,OAAO,CAAA,OAAQ,KAAK,iBAAiB,GAAG,IACxC,KAAK,CAAC,GAAG,IAAM,CAAC,EAAE,UAAU,IAAI,CAAC,IAAI,CAAC,EAAE,UAAU,IAAI,CAAC,IACvD,MAAM,GAAG,IACT,IAAI,CAAA,OAAQ,CAAC;oBACb,MAAM,KAAK,QAAQ,EAAE,QAAQ;oBAC7B,kBAAkB,KAAK,iBAAiB;oBACxC,WAAW,KAAK,UAAU,IAAI;gBAChC,CAAC,MAAM,EAAE;YAEX,SAAS;gBACP;gBACA;gBACA,iBAAiB,mBAAmB,mBAAmB;gBACvD,cAAc,iBAAiB,gBAAgB;gBAC/C;gBACA;gBACA,kBAAkB,oBAAoB,EAAE;YAC1C;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,kCAAkC;QAClD,SAAU;YACR,WAAW;QACb;IACF;IAEA,IAAI,SAAS;QACX,qBACE,8OAAC;YAAI,WAAU;;8BACb,8OAAC,gIAAA,CAAA,UAAU;;;;;8BACX,8OAAC;oBAAI,WAAU;8BACb,cAAA,8OAAC;wBAAI,WAAU;;;;;;;;;;;;;;;;;IAIvB;IAEA,IAAI,CAAC,sHAAA,CAAA,uBAAoB,EAAE;QACzB,qBACE,8OAAC;YAAI,WAAU;;8BACb,8OAAC,gIAAA,CAAA,UAAU;;;;;8BACX,8OAAC;oBAAI,WAAU;8BACb,cAAA,8OAAC;wBAAI,WAAU;kCACb,cAAA,8OAAC;4BAAI,WAAU;sCACb,cAAA,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAG,WAAU;kDAAwC;;;;;;kDAEtD,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAG,WAAU;0DAA2C;;;;;;0DACzD,8OAAC;gDAAE,WAAU;0DAAqB;;;;;;0DAIlC,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;;0EACC,8OAAC;gEAAG,WAAU;0EAA8B;;;;;;0EAC5C,8OAAC;gEAAE,WAAU;;oEAAgB;kFAAM,8OAAC;wEAAE,MAAK;wEAAuB,QAAO;wEAAS,KAAI;wEAAsB,WAAU;kFAAY;;;;;;oEAAgB;;;;;;;;;;;;;kEAGpJ,8OAAC;;0EACC,8OAAC;gEAAG,WAAU;0EAA8B;;;;;;0EAC5C,8OAAC;gEAAE,WAAU;;oEAAgB;kFAAmD,8OAAC;wEAAK,WAAU;kFAAkC;;;;;;;;;;;;;;;;;;kEAGpI,8OAAC;;0EACC,8OAAC;gEAAG,WAAU;0EAA8B;;;;;;0EAC5C,8OAAC;gEAAE,WAAU;;oEAAgB;kFAAY,8OAAC;wEAAK,WAAU;kFAAkC;;;;;;oEAAiB;;;;;;;;;;;;;kEAG9G,8OAAC;;0EACC,8OAAC;gEAAG,WAAU;0EAA8B;;;;;;0EAC5C,8OAAC;gEAAE,WAAU;0EAAgB;;;;;;;;;;;;;;;;;;;;;;;;kDAKnC,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAG,WAAU;0DAAoC;;;;;;0DAClD,8OAAC;gDAAG,WAAU;;kEACZ,8OAAC;kEAAG;;;;;;kEACJ,8OAAC;kEAAG;;;;;;kEACJ,8OAAC;kEAAG;;;;;;kEACJ,8OAAC;kEAAG;;;;;;kEACJ,8OAAC;kEAAG;;;;;;kEACJ,8OAAC;kEAAG;;;;;;;;;;;;;;;;;;kDAIR,8OAAC;wCAAI,WAAU;kDACb,cAAA,8OAAC;4CAAE,WAAU;;gDAAgB;8DACN,8OAAC;8DAAO;;;;;;gDAAiB;8DAAuD,8OAAC;oDAAK,WAAU;8DAAkC;;;;;;gDAAgB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;IASzL;IAEA,qBACE,8OAAC;QAAI,WAAU;;0BACb,8OAAC,gIAAA,CAAA,UAAU;;;;;0BAEX,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC;oBAAI,WAAU;;sCAEb,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAG,WAAU;8CAAmC;;;;;;8CACjD,8OAAC;oCAAE,WAAU;8CAAqB;;;;;;;;;;;;sCAIpC,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAI,WAAU;8CACb,cAAA,8OAAC;wCAAI,WAAU;kDACb,cAAA,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAI,WAAU;8DACb,cAAA,8OAAC;wDAAI,WAAU;kEACb,cAAA,8OAAC;4DAAI,WAAU;4DAAqB,MAAK;4DAAO,QAAO;4DAAe,SAAQ;sEAC5E,cAAA,8OAAC;gEAAK,eAAc;gEAAQ,gBAAe;gEAAQ,aAAa;gEAAG,GAAE;;;;;;;;;;;;;;;;;;;;;8DAI3E,8OAAC;oDAAI,WAAU;8DACb,cAAA,8OAAC;;0EACC,8OAAC;gEAAG,WAAU;0EAA6C;;;;;;0EAC3D,8OAAC;gEAAG,WAAU;0EAAqC,MAAM,eAAe;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;8CAOlF,8OAAC;oCAAI,WAAU;8CACb,cAAA,8OAAC;wCAAI,WAAU;kDACb,cAAA,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAI,WAAU;8DACb,cAAA,8OAAC;wDAAI,WAAU;kEACb,cAAA,8OAAC;4DAAI,WAAU;4DAAqB,MAAK;4DAAO,QAAO;4DAAe,SAAQ;sEAC5E,cAAA,8OAAC;gEAAK,eAAc;gEAAQ,gBAAe;gEAAQ,aAAa;gEAAG,GAAE;;;;;;;;;;;;;;;;;;;;;8DAI3E,8OAAC;oDAAI,WAAU;8DACb,cAAA,8OAAC;;0EACC,8OAAC;gEAAG,WAAU;0EAA6C;;;;;;0EAC3D,8OAAC;gEAAG,WAAU;0EAAqC,MAAM,YAAY;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;8CAO/E,8OAAC;oCAAI,WAAU;8CACb,cAAA,8OAAC;wCAAI,WAAU;kDACb,cAAA,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAI,WAAU;8DACb,cAAA,8OAAC;wDAAI,WAAU;kEACb,cAAA,8OAAC;4DAAI,WAAU;4DAAqB,MAAK;4DAAO,QAAO;4DAAe,SAAQ;sEAC5E,cAAA,8OAAC;gEAAK,eAAc;gEAAQ,gBAAe;gEAAQ,aAAa;gEAAG,GAAE;;;;;;;;;;;;;;;;;;;;;8DAI3E,8OAAC;oDAAI,WAAU;8DACb,cAAA,8OAAC;;0EACC,8OAAC;gEAAG,WAAU;0EAA6C;;;;;;0EAC3D,8OAAC;gEAAG,WAAU;0EAAqC,MAAM,aAAa;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;8CAOhF,8OAAC;oCAAI,WAAU;8CACb,cAAA,8OAAC;wCAAI,WAAU;kDACb,cAAA,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAI,WAAU;8DACb,cAAA,8OAAC;wDAAI,WAAU;kEACb,cAAA,8OAAC;4DAAI,WAAU;4DAAqB,MAAK;4DAAO,QAAO;4DAAe,SAAQ;sEAC5E,cAAA,8OAAC;gEAAK,eAAc;gEAAQ,gBAAe;gEAAQ,aAAa;gEAAG,GAAE;;;;;;;;;;;;;;;;;;;;;8DAI3E,8OAAC;oDAAI,WAAU;8DACb,cAAA,8OAAC;;0EACC,8OAAC;gEAAG,WAAU;0EAA6C;;;;;;0EAC3D,8OAAC;gEAAG,WAAU;;oEAAoC;oEAAE,MAAM,mBAAmB,CAAC,cAAc;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;sCASxG,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAG,WAAU;8CAAyC;;;;;;8CACvD,8OAAC;oCAAI,WAAU;8CACb,cAAA,8OAAC,mKAAA,CAAA,sBAAmB;wCAAC,OAAM;wCAAO,QAAO;kDACvC,cAAA,8OAAC,oJAAA,CAAA,WAAQ;4CAAC,MAAM,MAAM,UAAU;;8DAC9B,8OAAC,6JAAA,CAAA,gBAAa;oDAAC,iBAAgB;;;;;;8DAC/B,8OAAC,qJAAA,CAAA,QAAK;oDAAC,SAAQ;;;;;;8DACf,8OAAC,qJAAA,CAAA,QAAK;;;;;8DACN,8OAAC,uJAAA,CAAA,UAAO;;;;;8DACR,8OAAC,mJAAA,CAAA,MAAG;oDAAC,SAAQ;oDAAa,MAAK;oDAAU,MAAK;;;;;;8DAC9C,8OAAC,mJAAA,CAAA,MAAG;oDAAC,SAAQ;oDAAU,MAAK;oDAAU,MAAK;;;;;;;;;;;;;;;;;;;;;;;;;;;;sCAOnD,8OAAC;4BAAI,WAAU;;8CAEb,8OAAC;oCAAI,WAAU;8CACb,cAAA,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAG,WAAU;0DAAmD;;;;;;4CAChE,MAAM,WAAW,CAAC,MAAM,GAAG,kBAC1B,8OAAC;gDAAI,WAAU;0DACZ,MAAM,WAAW,CAAC,GAAG,CAAC,CAAC,SAAS,sBAC/B,8OAAC;wDAAgB,WAAU;;0EACzB,8OAAC;;kFACC,8OAAC;wEAAE,WAAU;kFAAqC,QAAQ,IAAI;;;;;;kFAC9D,8OAAC;wEAAE,WAAU;;4EAAyB,QAAQ,gBAAgB;4EAAC;;;;;;;;;;;;;0EAEjE,8OAAC;gEAAI,WAAU;0EACb,cAAA,8OAAC;oEAAE,WAAU;;wEAAoC;wEAAE,QAAQ,SAAS,CAAC,cAAc;;;;;;;;;;;;;uDAN7E;;;;;;;;;qEAYd,8OAAC;gDAAE,WAAU;0DAAgB;;;;;;;;;;;;;;;;;8CAMnC,8OAAC;oCAAI,WAAU;8CACb,cAAA,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAG,WAAU;0DAAmD;;;;;;4CAChE,MAAM,gBAAgB,CAAC,MAAM,GAAG,kBAC/B,8OAAC;gDAAI,WAAU;0DACZ,MAAM,gBAAgB,CAAC,GAAG,CAAC,CAAC,UAAU,sBACrC,8OAAC;wDAAgB,WAAU;;0EACzB,8OAAC;;kFACC,8OAAC;wEAAE,WAAU;kFACV,CAAA,GAAA,qJAAA,CAAA,SAAM,AAAD,EAAE,IAAI,KAAK,SAAS,IAAI,GAAG;;;;;;kFAEnC,8OAAC;wEAAE,WAAU;;4EAAwB;4EAAS,SAAS,YAAY;;;;;;;;;;;;;0EAErE,8OAAC;gEAAI,WAAU;0EACb,cAAA,8OAAC;oEAAE,WAAU;;wEAAqC;wEAAY,SAAS,eAAe;;;;;;;;;;;;;uDARhF;;;;;;;;;qEAcd,8OAAC;gDAAE,WAAU;0DAAgB;;;;;;;;;;;;;;;;;;;;;;;sCAOrC,8OAAC;4BAAI,WAAU;;8CACb,8OAAC,4JAAA,CAAA,UAAI;oCAAC,MAAK;oCAAc,WAAU;8CACjC,cAAA,8OAAC;wCAAI,WAAU;kDACb,cAAA,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAI,WAAU;8DACb,cAAA,8OAAC;wDAAI,WAAU;kEACb,cAAA,8OAAC;4DAAI,WAAU;4DAAqB,MAAK;4DAAO,QAAO;4DAAe,SAAQ;sEAC5E,cAAA,8OAAC;gEAAK,eAAc;gEAAQ,gBAAe;gEAAQ,aAAa;gEAAG,GAAE;;;;;;;;;;;;;;;;;;;;;8DAI3E,8OAAC;oDAAI,WAAU;;sEACb,8OAAC;4DAAG,WAAU;sEAAoC;;;;;;sEAClD,8OAAC;4DAAE,WAAU;sEAAwB;;;;;;;;;;;;;;;;;;;;;;;;;;;;8CAM7C,8OAAC,4JAAA,CAAA,UAAI;oCAAC,MAAK;oCAAW,WAAU;8CAC9B,cAAA,8OAAC;wCAAI,WAAU;kDACb,cAAA,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAI,WAAU;8DACb,cAAA,8OAAC;wDAAI,WAAU;kEACb,cAAA,8OAAC;4DAAI,WAAU;4DAAqB,MAAK;4DAAO,QAAO;4DAAe,SAAQ;sEAC5E,cAAA,8OAAC;gEAAK,eAAc;gEAAQ,gBAAe;gEAAQ,aAAa;gEAAG,GAAE;;;;;;;;;;;;;;;;;;;;;8DAI3E,8OAAC;oDAAI,WAAU;;sEACb,8OAAC;4DAAG,WAAU;sEAAoC;;;;;;sEAClD,8OAAC;4DAAE,WAAU;sEAAwB;;;;;;;;;;;;;;;;;;;;;;;;;;;;8CAM7C,8OAAC,4JAAA,CAAA,UAAI;oCAAC,MAAK;oCAAY,WAAU;8CAC/B,cAAA,8OAAC;wCAAI,WAAU;kDACb,cAAA,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAI,WAAU;8DACb,cAAA,8OAAC;wDAAI,WAAU;kEACb,cAAA,8OAAC;4DAAI,WAAU;4DAAqB,MAAK;4DAAO,QAAO;4DAAe,SAAQ;sEAC5E,cAAA,8OAAC;gEAAK,eAAc;gEAAQ,gBAAe;gEAAQ,aAAa;gEAAG,GAAE;;;;;;;;;;;;;;;;;;;;;8DAI3E,8OAAC;oDAAI,WAAU;;sEACb,8OAAC;4DAAG,WAAU;sEAAoC;;;;;;sEAClD,8OAAC;4DAAE,WAAU;sEAAwB;;;;;;;;;;;;;;;;;;;;;;;;;;;;8CAM7C,8OAAC,4JAAA,CAAA,UAAI;oCAAC,MAAK;oCAAY,WAAU;8CAC/B,cAAA,8OAAC;wCAAI,WAAU;kDACb,cAAA,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAI,WAAU;8DACb,cAAA,8OAAC;wDAAI,WAAU;kEACb,cAAA,8OAAC;4DAAI,WAAU;4DAAqB,MAAK;4DAAO,QAAO;4DAAe,SAAQ;sEAC5E,cAAA,8OAAC;gEAAK,eAAc;gEAAQ,gBAAe;gEAAQ,aAAa;gEAAG,GAAE;;;;;;;;;;;;;;;;;;;;;8DAI3E,8OAAC;oDAAI,WAAU;;sEACb,8OAAC;4DAAG,WAAU;sEAAoC;;;;;;sEAClD,8OAAC;4DAAE,WAAU;sEAAwB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAUzD", "debugId": null}}]}