'use client'

import { useState } from 'react'
import Navigation from '@/components/Navigation'
import { supabase } from '@/lib/supabase'
import { DocumentArrowUpIcon, TrashIcon } from '@heroicons/react/24/outline'

export default function SettingsPage() {
  const [importing, setImporting] = useState(false)
  const [exporting, setExporting] = useState(false)

  const exportAllData = async () => {
    setExporting(true)
    try {
      // Fetch all data
      const [productsResult, deliveriesResult, deliveryItemsResult] = await Promise.all([
        supabase.from('products').select('*').order('name'),
        supabase.from('deliveries').select('*').order('date', { ascending: false }),
        supabase.from('delivery_items').select('*, product:products(name, sku)').order('created_at')
      ])

      const data = {
        products: productsResult.data || [],
        deliveries: deliveriesResult.data || [],
        deliveryItems: deliveryItemsResult.data || [],
        exportDate: new Date().toISOString(),
        version: '1.0'
      }

      // Create and download JSON file
      const blob = new Blob([JSON.stringify(data, null, 2)], { type: 'application/json' })
      const url = window.URL.createObjectURL(blob)
      const a = document.createElement('a')
      a.href = url
      a.download = `inventory-backup-${new Date().toISOString().split('T')[0]}.json`
      a.click()
      window.URL.revokeObjectURL(url)
    } catch (error) {
      console.error('Error exporting data:', error)
      alert('Error exporting data. Please try again.')
    } finally {
      setExporting(false)
    }
  }

  const handleFileImport = async (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0]
    if (!file) return

    setImporting(true)
    try {
      const text = await file.text()
      const data = JSON.parse(text)

      if (!data.products || !data.deliveries) {
        throw new Error('Invalid file format')
      }

      // Import products first
      if (data.products.length > 0) {
        const { error: productsError } = await supabase
          .from('products')
          .upsert(data.products.map((p: any) => ({
            id: p.id,
            sku: p.sku,
            name: p.name,
            category: p.category,
            price: p.price,
            description: p.description
          })))

        if (productsError) throw productsError
      }

      // Import deliveries
      if (data.deliveries.length > 0) {
        const { error: deliveriesError } = await supabase
          .from('deliveries')
          .upsert(data.deliveries.map((d: any) => ({
            id: d.id,
            date: d.date,
            pickup_count: d.pickup_count,
            delivered_count: d.delivered_count,
            returned_count: d.returned_count,
            courier: d.courier,
            notes: d.notes
          })))

        if (deliveriesError) throw deliveriesError
      }

      // Import delivery items
      if (data.deliveryItems && data.deliveryItems.length > 0) {
        const { error: itemsError } = await supabase
          .from('delivery_items')
          .upsert(data.deliveryItems.map((item: any) => ({
            id: item.id,
            delivery_id: item.delivery_id,
            product_id: item.product_id,
            quantity: item.quantity
          })))

        if (itemsError) throw itemsError
      }

      alert('Data imported successfully!')
    } catch (error) {
      console.error('Error importing data:', error)
      alert('Error importing data. Please check the file format and try again.')
    } finally {
      setImporting(false)
      // Reset file input
      event.target.value = ''
    }
  }

  const clearAllData = async () => {
    if (!confirm('Are you sure you want to delete ALL data? This action cannot be undone!')) return
    if (!confirm('This will permanently delete all products, deliveries, and related data. Are you absolutely sure?')) return

    try {
      // Delete in order due to foreign key constraints
      await supabase.from('delivery_items').delete().neq('id', '00000000-0000-0000-0000-000000000000')
      await supabase.from('deliveries').delete().neq('id', '00000000-0000-0000-0000-000000000000')
      await supabase.from('products').delete().neq('id', '00000000-0000-0000-0000-000000000000')

      alert('All data has been cleared successfully.')
    } catch (error) {
      console.error('Error clearing data:', error)
      alert('Error clearing data. Please try again.')
    }
  }

  return (
    <div className="min-h-screen bg-gray-50">
      <Navigation />
      
      <div className="max-w-7xl mx-auto py-6 sm:px-6 lg:px-8">
        <div className="px-4 py-6 sm:px-0">
          <h1 className="text-3xl font-bold text-gray-900 mb-8">Settings</h1>

          {/* Database Setup Instructions */}
          <div className="bg-white shadow rounded-lg mb-8">
            <div className="px-4 py-5 sm:p-6">
              <h3 className="text-lg leading-6 font-medium text-gray-900 mb-4">Database Setup</h3>
              <div className="prose max-w-none">
                <p className="text-gray-600 mb-4">
                  To use this inventory management system, you need to set up a Supabase database. Follow these steps:
                </p>
                <ol className="list-decimal list-inside space-y-2 text-gray-600">
                  <li>Go to <a href="https://supabase.com" target="_blank" rel="noopener noreferrer" className="text-blue-600 hover:text-blue-800">supabase.com</a> and create a free account</li>
                  <li>Create a new project</li>
                  <li>Go to the SQL Editor in your Supabase dashboard</li>
                  <li>Copy and paste the SQL commands from the <code className="bg-gray-100 px-1 py-0.5 rounded">database-setup.sql</code> file in your project root</li>
                  <li>Run the SQL commands to create the database tables</li>
                  <li>Go to Settings → API in your Supabase dashboard</li>
                  <li>Copy your Project URL and anon public key</li>
                  <li>Update the <code className="bg-gray-100 px-1 py-0.5 rounded">.env.local</code> file with your Supabase credentials</li>
                  <li>Restart your development server</li>
                </ol>
                <div className="mt-4 p-4 bg-blue-50 rounded-md">
                  <p className="text-blue-800 text-sm">
                    <strong>Note:</strong> The database setup file includes sample products to get you started. 
                    You can modify or delete these after setup.
                  </p>
                </div>
              </div>
            </div>
          </div>

          {/* Data Management */}
          <div className="bg-white shadow rounded-lg mb-8">
            <div className="px-4 py-5 sm:p-6">
              <h3 className="text-lg leading-6 font-medium text-gray-900 mb-4">Data Management</h3>
              
              <div className="space-y-6">
                {/* Export Data */}
                <div>
                  <h4 className="text-md font-medium text-gray-900 mb-2">Export Data</h4>
                  <p className="text-gray-600 mb-4">
                    Export all your inventory data as a JSON backup file. This includes products, deliveries, and delivery items.
                  </p>
                  <button
                    onClick={exportAllData}
                    disabled={exporting}
                    className="bg-green-600 hover:bg-green-700 disabled:bg-green-400 text-white px-4 py-2 rounded-md flex items-center space-x-2"
                  >
                    <DocumentArrowUpIcon className="h-5 w-5" />
                    <span>{exporting ? 'Exporting...' : 'Export All Data'}</span>
                  </button>
                </div>

                {/* Import Data */}
                <div>
                  <h4 className="text-md font-medium text-gray-900 mb-2">Import Data</h4>
                  <p className="text-gray-600 mb-4">
                    Import data from a previously exported JSON backup file. This will merge with existing data.
                  </p>
                  <div className="flex items-center space-x-4">
                    <label className="bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-md cursor-pointer flex items-center space-x-2">
                      <DocumentArrowUpIcon className="h-5 w-5" />
                      <span>{importing ? 'Importing...' : 'Import Data'}</span>
                      <input
                        type="file"
                        accept=".json"
                        onChange={handleFileImport}
                        disabled={importing}
                        className="hidden"
                      />
                    </label>
                  </div>
                </div>

                {/* Clear All Data */}
                <div>
                  <h4 className="text-md font-medium text-gray-900 mb-2">Clear All Data</h4>
                  <p className="text-gray-600 mb-4">
                    <strong className="text-red-600">Warning:</strong> This will permanently delete all products, deliveries, and related data. This action cannot be undone.
                  </p>
                  <button
                    onClick={clearAllData}
                    className="bg-red-600 hover:bg-red-700 text-white px-4 py-2 rounded-md flex items-center space-x-2"
                  >
                    <TrashIcon className="h-5 w-5" />
                    <span>Clear All Data</span>
                  </button>
                </div>
              </div>
            </div>
          </div>

          {/* System Information */}
          <div className="bg-white shadow rounded-lg">
            <div className="px-4 py-5 sm:p-6">
              <h3 className="text-lg leading-6 font-medium text-gray-900 mb-4">System Information</h3>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div>
                  <h4 className="text-md font-medium text-gray-900 mb-2">Application</h4>
                  <dl className="space-y-1">
                    <div className="flex justify-between">
                      <dt className="text-sm text-gray-600">Version:</dt>
                      <dd className="text-sm text-gray-900">1.0.0</dd>
                    </div>
                    <div className="flex justify-between">
                      <dt className="text-sm text-gray-600">Framework:</dt>
                      <dd className="text-sm text-gray-900">Next.js 15</dd>
                    </div>
                    <div className="flex justify-between">
                      <dt className="text-sm text-gray-600">Database:</dt>
                      <dd className="text-sm text-gray-900">Supabase</dd>
                    </div>
                  </dl>
                </div>
                <div>
                  <h4 className="text-md font-medium text-gray-900 mb-2">Features</h4>
                  <ul className="text-sm text-gray-600 space-y-1">
                    <li>• Delivery tracking and management</li>
                    <li>• Product inventory management</li>
                    <li>• Performance reports and analytics</li>
                    <li>• Data export and import</li>
                    <li>• Responsive design for mobile and desktop</li>
                  </ul>
                </div>
              </div>
            </div>
          </div>

          {/* Deployment Instructions */}
          <div className="bg-white shadow rounded-lg mt-8">
            <div className="px-4 py-5 sm:p-6">
              <h3 className="text-lg leading-6 font-medium text-gray-900 mb-4">Deployment</h3>
              <div className="prose max-w-none">
                <p className="text-gray-600 mb-4">
                  To deploy this application for free, you can use Vercel:
                </p>
                <ol className="list-decimal list-inside space-y-2 text-gray-600">
                  <li>Push your code to a GitHub repository</li>
                  <li>Go to <a href="https://vercel.com" target="_blank" rel="noopener noreferrer" className="text-blue-600 hover:text-blue-800">vercel.com</a> and sign up with your GitHub account</li>
                  <li>Import your repository</li>
                  <li>Add your environment variables (NEXT_PUBLIC_SUPABASE_URL and NEXT_PUBLIC_SUPABASE_ANON_KEY)</li>
                  <li>Deploy the application</li>
                </ol>
                <div className="mt-4 p-4 bg-green-50 rounded-md">
                  <p className="text-green-800 text-sm">
                    <strong>Free hosting:</strong> Both Supabase and Vercel offer generous free tiers that are perfect for small to medium businesses.
                  </p>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  )
}
