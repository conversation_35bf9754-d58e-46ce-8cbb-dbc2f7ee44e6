import { createClient } from '@supabase/supabase-js'

const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL || 'https://placeholder.supabase.co'
const supabaseAnonKey = process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY || 'placeholder-key'

// Check if environment variables are properly configured
export const isSupabaseConfigured =
  supabaseUrl !== 'https://placeholder.supabase.co' &&
  supabaseUrl !== 'your_supabase_url_here' &&
  supabaseAnonKey !== 'placeholder-key' &&
  supabaseAnonKey !== 'your_supabase_anon_key_here'

export const supabase = createClient(supabaseUrl, supabaseAnonKey)

// Database Types
export interface Product {
  id: string
  sku: string
  name: string
  category?: string
  price?: number
  description?: string
  created_at: string
  updated_at: string
}

export interface Delivery {
  id: string
  date: string
  pickup_count: number
  delivered_count: number
  returned_count: number
  courier?: string
  notes?: string
  created_at: string
  updated_at: string
}

export interface DeliveryItem {
  id: string
  delivery_id: string
  product_id: string
  quantity: number
  product?: Product
}

export interface DeliveryWithItems extends Delivery {
  delivery_items: DeliveryItem[]
}
