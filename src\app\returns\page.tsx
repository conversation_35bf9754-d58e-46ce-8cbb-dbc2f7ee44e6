'use client'

import { useEffect, useState } from 'react'
import Navigation from '@/components/Navigation'
import { supabase, isSupabaseConfigured } from '@/lib/supabase'
import { format } from 'date-fns'

interface Return {
  id: string
  date: string
  return_count: number
  notes: string
  return_items: Array<{
    product_id: string
    quantity_returned: number
    products: {
      name: string
    }
  }>
}

interface Product {
  id: string
  name: string
}

export default function ReturnsPage() {
  const [returns, setReturns] = useState<Return[]>([])
  const [products, setProducts] = useState<Product[]>([])
  const [loading, setLoading] = useState(true)
  const [showForm, setShowForm] = useState(false)
  const [formData, setFormData] = useState({
    date: new Date().toISOString().split('T')[0],
    notes: '',
    items: [] as Array<{ product_id: string, quantity: number }>
  })

  useEffect(() => {
    fetchData()
  }, [])

  const fetchData = async () => {
    if (!isSupabaseConfigured) {
      setLoading(false)
      return
    }

    try {
      // Fetch returns with items
      const { data: returnsData } = await supabase
        .from('returns')
        .select(`
          *,
          return_items (
            product_id,
            quantity_returned,
            products (name)
          )
        `)
        .order('date', { ascending: false })

      // Fetch products
      const { data: productsData } = await supabase
        .from('products')
        .select('id, name')
        .eq('is_active', true)
        .order('name')

      setReturns(returnsData || [])
      setProducts(productsData || [])
    } catch (error) {
      console.error('Error fetching data:', error)
    } finally {
      setLoading(false)
    }
  }

  const addItem = () => {
    setFormData(prev => ({
      ...prev,
      items: [...prev.items, { product_id: '', quantity: 0 }]
    }))
  }

  const removeItem = (index: number) => {
    setFormData(prev => ({
      ...prev,
      items: prev.items.filter((_, i) => i !== index)
    }))
  }

  const updateItem = (index: number, field: string, value: string | number) => {
    setFormData(prev => ({
      ...prev,
      items: prev.items.map((item, i) => 
        i === index ? { ...item, [field]: value } : item
      )
    }))
  }

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    
    try {
      const totalReturns = formData.items.reduce((sum, item) => sum + item.quantity, 0)
      
      // Insert return record
      const { data: returnData, error: returnError } = await supabase
        .from('returns')
        .insert({
          date: formData.date,
          return_count: totalReturns,
          notes: formData.notes
        })
        .select()
        .single()

      if (returnError) throw returnError

      // Insert return items
      if (formData.items.length > 0) {
        const returnItems = formData.items
          .filter(item => item.product_id && item.quantity > 0)
          .map(item => ({
            return_id: returnData.id,
            product_id: item.product_id,
            quantity_returned: item.quantity
          }))

        if (returnItems.length > 0) {
          const { error: itemsError } = await supabase
            .from('return_items')
            .insert(returnItems)

          if (itemsError) throw itemsError
        }
      }

      // Update inventory
      for (const item of formData.items) {
        if (item.product_id && item.quantity > 0) {
          const { error: inventoryError } = await supabase.rpc('update_inventory_returns', {
            p_product_id: item.product_id,
            p_quantity: item.quantity
          })
          
          if (inventoryError) console.error('Error updating inventory:', inventoryError)
        }
      }

      // Reset form and refresh data
      setFormData({
        date: new Date().toISOString().split('T')[0],
        notes: '',
        items: []
      })
      setShowForm(false)
      fetchData()
    } catch (error) {
      console.error('Error creating return:', error)
      alert('Error creating return. Please try again.')
    }
  }

  if (loading) {
    return (
      <div className="min-h-screen bg-gray-50">
        <Navigation />
        <div className="flex items-center justify-center h-96">
          <div className="animate-spin rounded-full h-32 w-32 border-b-2 border-blue-500"></div>
        </div>
      </div>
    )
  }

  if (!isSupabaseConfigured) {
    return (
      <div className="min-h-screen bg-gray-50">
        <Navigation />
        <div className="max-w-4xl mx-auto py-6 sm:px-6 lg:px-8">
          <div className="text-center">
            <h1 className="text-2xl font-bold text-gray-900">Database Not Configured</h1>
            <p className="mt-2 text-gray-600">Please configure your Supabase database to use this feature.</p>
          </div>
        </div>
      </div>
    )
  }

  return (
    <div className="min-h-screen bg-gray-50">
      <Navigation />
      
      <div className="max-w-7xl mx-auto py-6 sm:px-6 lg:px-8">
        <div className="px-4 py-6 sm:px-0">
          <div className="flex justify-between items-center mb-6">
            <h1 className="text-3xl font-bold text-gray-900">Returns Management</h1>
            <button
              onClick={() => setShowForm(!showForm)}
              className="bg-red-600 hover:bg-red-700 text-white font-bold py-2 px-4 rounded"
            >
              {showForm ? 'Cancel' : 'Add Return'}
            </button>
          </div>

          {showForm && (
            <div className="bg-white shadow rounded-lg p-6 mb-6">
              <h2 className="text-lg font-medium text-gray-900 mb-4">Add New Return</h2>
              <form onSubmit={handleSubmit} className="space-y-4">
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div>
                    <label className="block text-sm font-medium text-gray-700">Date</label>
                    <input
                      type="date"
                      value={formData.date}
                      onChange={(e) => setFormData(prev => ({ ...prev, date: e.target.value }))}
                      className="mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:ring-red-500 focus:border-red-500"
                      required
                    />
                  </div>
                  <div>
                    <label className="block text-sm font-medium text-gray-700">Notes</label>
                    <input
                      type="text"
                      value={formData.notes}
                      onChange={(e) => setFormData(prev => ({ ...prev, notes: e.target.value }))}
                      className="mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:ring-red-500 focus:border-red-500"
                      placeholder="Optional notes"
                    />
                  </div>
                </div>

                <div>
                  <div className="flex justify-between items-center mb-2">
                    <label className="block text-sm font-medium text-gray-700">Return Items</label>
                    <button
                      type="button"
                      onClick={addItem}
                      className="bg-gray-600 hover:bg-gray-700 text-white text-sm px-3 py-1 rounded"
                    >
                      Add Item
                    </button>
                  </div>
                  
                  {formData.items.map((item, index) => (
                    <div key={index} className="flex gap-4 items-center mb-2">
                      <select
                        value={item.product_id}
                        onChange={(e) => updateItem(index, 'product_id', e.target.value)}
                        className="flex-1 border-gray-300 rounded-md shadow-sm focus:ring-red-500 focus:border-red-500"
                        required
                      >
                        <option value="">Select Product</option>
                        {products.map(product => (
                          <option key={product.id} value={product.id}>{product.name}</option>
                        ))}
                      </select>
                      <input
                        type="number"
                        value={item.quantity}
                        onChange={(e) => updateItem(index, 'quantity', parseInt(e.target.value) || 0)}
                        placeholder="Quantity"
                        className="w-24 border-gray-300 rounded-md shadow-sm focus:ring-red-500 focus:border-red-500"
                        min="1"
                        required
                      />
                      <button
                        type="button"
                        onClick={() => removeItem(index)}
                        className="bg-red-600 hover:bg-red-700 text-white px-2 py-1 rounded text-sm"
                      >
                        Remove
                      </button>
                    </div>
                  ))}
                </div>

                <div className="flex justify-end space-x-3">
                  <button
                    type="button"
                    onClick={() => setShowForm(false)}
                    className="bg-gray-300 hover:bg-gray-400 text-gray-800 font-bold py-2 px-4 rounded"
                  >
                    Cancel
                  </button>
                  <button
                    type="submit"
                    className="bg-red-600 hover:bg-red-700 text-white font-bold py-2 px-4 rounded"
                  >
                    Add Return
                  </button>
                </div>
              </form>
            </div>
          )}

          {/* Returns List */}
          <div className="bg-white shadow rounded-lg">
            <div className="px-4 py-5 sm:p-6">
              <h3 className="text-lg leading-6 font-medium text-gray-900 mb-4">Recent Returns</h3>
              {returns.length > 0 ? (
                <div className="overflow-x-auto">
                  <table className="min-w-full divide-y divide-gray-200">
                    <thead className="bg-gray-50">
                      <tr>
                        <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Date</th>
                        <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Total Returns</th>
                        <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Products</th>
                        <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Notes</th>
                      </tr>
                    </thead>
                    <tbody className="bg-white divide-y divide-gray-200">
                      {returns.map((returnRecord) => (
                        <tr key={returnRecord.id}>
                          <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                            {format(new Date(returnRecord.date), 'MMM dd, yyyy')}
                          </td>
                          <td className="px-6 py-4 whitespace-nowrap text-sm text-red-600 font-medium">
                            {returnRecord.return_count}
                          </td>
                          <td className="px-6 py-4 text-sm text-gray-900">
                            {returnRecord.return_items?.map((item, index) => (
                              <div key={index} className="text-xs">
                                {item.products?.name}: {item.quantity_returned}
                              </div>
                            ))}
                          </td>
                          <td className="px-6 py-4 text-sm text-gray-500">
                            {returnRecord.notes || '-'}
                          </td>
                        </tr>
                      ))}
                    </tbody>
                  </table>
                </div>
              ) : (
                <p className="text-gray-500">No returns recorded yet.</p>
              )}
            </div>
          </div>
        </div>
      </div>
    </div>
  )
}
