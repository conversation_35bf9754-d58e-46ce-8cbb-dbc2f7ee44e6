'use client'

import { useEffect, useState } from 'react'
import Navigation from '@/components/Navigation'
import { supabase, isSupabaseConfigured } from '@/lib/supabase'

interface InventoryItem {
  id: string
  product_id: string
  purchased: number
  processed: number
  delivered: number
  returned: number
  current_inventory: number
  total_cost: number
  products: {
    name: string
    cost_per_unit: number
  }
}

interface PurchaseFormData {
  product_id: string
  quantity: number
  cost_per_unit: number
  purchase_date: string
}

export default function InventoryPage() {
  const [inventory, setInventory] = useState<InventoryItem[]>([])
  const [products, setProducts] = useState<Array<{id: string, name: string}>>([])
  const [loading, setLoading] = useState(true)
  const [showPurchaseForm, setShowPurchaseForm] = useState(false)
  const [purchaseData, setPurchaseData] = useState<PurchaseFormData>({
    product_id: '',
    quantity: 0,
    cost_per_unit: 0,
    purchase_date: new Date().toISOString().split('T')[0]
  })

  useEffect(() => {
    fetchInventoryData()
  }, [])

  const fetchInventoryData = async () => {
    if (!isSupabaseConfigured) {
      setLoading(false)
      return
    }

    try {
      // Fetch inventory with product details
      const { data: inventoryData } = await supabase
        .from('inventory')
        .select(`
          *,
          products (name, cost_per_unit)
        `)
        .order('products(name)')

      // Fetch products for purchase form
      const { data: productsData } = await supabase
        .from('products')
        .select('id, name')
        .eq('is_active', true)
        .order('name')

      setInventory(inventoryData || [])
      setProducts(productsData || [])
    } catch (error) {
      console.error('Error fetching inventory:', error)
    } finally {
      setLoading(false)
    }
  }

  const handlePurchaseSubmit = async (e: React.FormEvent) => {
    e.preventDefault()

    try {
      // Add to monthly purchases
      const currentDate = new Date(purchaseData.purchase_date)
      const { error: purchaseError } = await supabase
        .from('monthly_purchases')
        .insert({
          product_id: purchaseData.product_id,
          month: currentDate.getMonth() + 1,
          year: currentDate.getFullYear(),
          quantity_purchased: purchaseData.quantity,
          cost_per_unit: purchaseData.cost_per_unit,
          purchase_date: purchaseData.purchase_date
        })

      if (purchaseError) throw purchaseError

      // Update inventory
      const { error: inventoryError } = await supabase.rpc('update_inventory_purchased', {
        p_product_id: purchaseData.product_id,
        p_quantity: purchaseData.quantity
      })

      if (inventoryError) throw inventoryError

      // Reset form and refresh data
      setPurchaseData({
        product_id: '',
        quantity: 0,
        cost_per_unit: 0,
        purchase_date: new Date().toISOString().split('T')[0]
      })
      setShowPurchaseForm(false)
      fetchInventoryData()
    } catch (error) {
      console.error('Error recording purchase:', error)
      alert('Error recording purchase. Please try again.')
    }
  }

  const totalInventoryValue = inventory.reduce((sum, item) => sum + (item.total_cost || 0), 0)

  if (loading) {
    return (
      <div className="min-h-screen bg-gray-50">
        <Navigation />
        <div className="flex items-center justify-center h-96">
          <div className="animate-spin rounded-full h-32 w-32 border-b-2 border-blue-500"></div>
        </div>
      </div>
    )
  }

  if (!isSupabaseConfigured) {
    return (
      <div className="min-h-screen bg-gray-50">
        <Navigation />
        <div className="max-w-4xl mx-auto py-6 sm:px-6 lg:px-8">
          <div className="text-center">
            <h1 className="text-2xl font-bold text-gray-900">Database Not Configured</h1>
            <p className="mt-2 text-gray-600">Please configure your Supabase database to use this feature.</p>
          </div>
        </div>
      </div>
    )
  }

  return (
    <div className="min-h-screen bg-gray-50">
      <Navigation />

      <div className="max-w-7xl mx-auto py-6 sm:px-6 lg:px-8">
        <div className="px-4 py-6 sm:px-0">
          <div className="flex justify-between items-center mb-6">
            <h1 className="text-3xl font-bold text-gray-900">Inventory Management</h1>
            <button
              onClick={() => setShowPurchaseForm(!showPurchaseForm)}
              className="bg-green-600 hover:bg-green-700 text-white font-bold py-2 px-4 rounded"
            >
              {showPurchaseForm ? 'Cancel' : 'Record Purchase'}
            </button>
          </div>

          {/* Summary Card */}
          <div className="bg-white overflow-hidden shadow rounded-lg mb-6">
            <div className="p-5">
              <div className="flex items-center">
                <div className="flex-shrink-0">
                  <div className="w-8 h-8 bg-green-500 rounded-md flex items-center justify-center">
                    <svg className="w-5 h-5 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1" />
                    </svg>
                  </div>
                </div>
                <div className="ml-5 w-0 flex-1">
                  <dl>
                    <dt className="text-sm font-medium text-gray-500 truncate">Total Inventory Value</dt>
                    <dd className="text-lg font-medium text-gray-900">৳{totalInventoryValue.toLocaleString()}</dd>
                  </dl>
                </div>
              </div>
            </div>
          </div>

          {/* Purchase Form */}
          {showPurchaseForm && (
            <div className="bg-white shadow rounded-lg p-6 mb-6">
              <h2 className="text-lg font-medium text-gray-900 mb-4">Record Purchase</h2>
              <form onSubmit={handlePurchaseSubmit} className="space-y-4">
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div>
                    <label className="block text-sm font-medium text-gray-700">Product</label>
                    <select
                      value={purchaseData.product_id}
                      onChange={(e) => setPurchaseData(prev => ({ ...prev, product_id: e.target.value }))}
                      className="mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:ring-green-500 focus:border-green-500"
                      required
                    >
                      <option value="">Select Product</option>
                      {products.map(product => (
                        <option key={product.id} value={product.id}>{product.name}</option>
                      ))}
                    </select>
                  </div>
                  <div>
                    <label className="block text-sm font-medium text-gray-700">Purchase Date</label>
                    <input
                      type="date"
                      value={purchaseData.purchase_date}
                      onChange={(e) => setPurchaseData(prev => ({ ...prev, purchase_date: e.target.value }))}
                      className="mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:ring-green-500 focus:border-green-500"
                      required
                    />
                  </div>
                </div>

                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div>
                    <label className="block text-sm font-medium text-gray-700">Quantity</label>
                    <input
                      type="number"
                      value={purchaseData.quantity}
                      onChange={(e) => setPurchaseData(prev => ({ ...prev, quantity: parseInt(e.target.value) || 0 }))}
                      className="mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:ring-green-500 focus:border-green-500"
                      min="1"
                      required
                    />
                  </div>
                  <div>
                    <label className="block text-sm font-medium text-gray-700">Cost per Unit (৳)</label>
                    <input
                      type="number"
                      step="0.01"
                      value={purchaseData.cost_per_unit}
                      onChange={(e) => setPurchaseData(prev => ({ ...prev, cost_per_unit: parseFloat(e.target.value) || 0 }))}
                      className="mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:ring-green-500 focus:border-green-500"
                      min="0"
                      required
                    />
                  </div>
                </div>

                <div className="flex justify-end space-x-3">
                  <button
                    type="button"
                    onClick={() => setShowPurchaseForm(false)}
                    className="bg-gray-300 hover:bg-gray-400 text-gray-800 font-bold py-2 px-4 rounded"
                  >
                    Cancel
                  </button>
                  <button
                    type="submit"
                    className="bg-green-600 hover:bg-green-700 text-white font-bold py-2 px-4 rounded"
                  >
                    Record Purchase
                  </button>
                </div>
              </form>
            </div>
          )}

          {/* Inventory Table */}
          <div className="bg-white shadow rounded-lg">
            <div className="px-4 py-5 sm:p-6">
              <h3 className="text-lg leading-6 font-medium text-gray-900 mb-4">Current Inventory</h3>
              {inventory.length > 0 ? (
                <div className="overflow-x-auto">
                  <table className="min-w-full divide-y divide-gray-200">
                    <thead className="bg-gray-50">
                      <tr>
                        <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Product</th>
                        <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Purchased</th>
                        <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Processed</th>
                        <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Delivered</th>
                        <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Returned</th>
                        <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Current</th>
                        <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Cost/Unit</th>
                        <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Total Cost</th>
                      </tr>
                    </thead>
                    <tbody className="bg-white divide-y divide-gray-200">
                      {inventory.map((item) => (
                        <tr key={item.id} className={item.current_inventory <= 5 ? 'bg-red-50' : ''}>
                          <td className="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">
                            {item.products?.name}
                          </td>
                          <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">{item.purchased}</td>
                          <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">{item.processed}</td>
                          <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">{item.delivered}</td>
                          <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">{item.returned}</td>
                          <td className="px-6 py-4 whitespace-nowrap text-sm font-medium">
                            <span className={`${item.current_inventory <= 5 ? 'text-red-600' : 'text-gray-900'}`}>
                              {item.current_inventory}
                            </span>
                          </td>
                          <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                            ৳{item.products?.cost_per_unit?.toLocaleString()}
                          </td>
                          <td className="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">
                            ৳{item.total_cost?.toLocaleString()}
                          </td>
                        </tr>
                      ))}
                    </tbody>
                  </table>
                </div>
              ) : (
                <p className="text-gray-500">No inventory data available.</p>
              )}
            </div>
          </div>
        </div>
      </div>
    </div>
  )
}
