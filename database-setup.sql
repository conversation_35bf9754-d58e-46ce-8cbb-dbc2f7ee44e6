-- Cosmetics/Pharmaceutical Inventory Management System Database Schema
-- Run these commands in your Supabase SQL Editor

-- Enable Row Level Security
-- ALTER DATABASE postgres SET "app.jwt_secret" TO 'your-jwt-secret'; -- Not needed for Supabase

-- Create Products Table with specific cosmetic/pharmaceutical products
CREATE TABLE IF NOT EXISTS products (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    name VARCHAR(255) UNIQUE NOT NULL,
    category VARCHAR(100) DEFAULT 'Cosmetics',
    cost_per_unit DECIMAL(10,2) NOT NULL DEFAULT 0,
    is_active BOOLEAN DEFAULT true,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create Inventory Table for tracking stock levels
CREATE TABLE IF NOT EXISTS inventory (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    product_id UUID REFERENCES products(id) ON DELETE CASCADE,
    purchased INTEGER NOT NULL DEFAULT 0,
    processed INTEGER NOT NULL DEFAULT 0,
    delivered INTEGER NOT NULL DEFAULT 0,
    returned INTEGER NOT NULL DEFAULT 0,
    current_inventory INTEGER GENERATED ALWAYS AS (purchased - processed - delivered + returned) STORED,
    total_cost DECIMAL(12,2) DEFAULT 0,
    last_updated TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    UNIQUE(product_id)
);

-- Create Daily Deliveries Table
CREATE TABLE IF NOT EXISTS daily_deliveries (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    date DATE NOT NULL,
    total_pickup INTEGER NOT NULL DEFAULT 0,
    total_delivered INTEGER NOT NULL DEFAULT 0,
    notes TEXT,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    UNIQUE(date)
);

-- Create Daily Delivery Items Table
CREATE TABLE IF NOT EXISTS daily_delivery_items (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    daily_delivery_id UUID REFERENCES daily_deliveries(id) ON DELETE CASCADE,
    product_id UUID REFERENCES products(id) ON DELETE CASCADE,
    quantity_delivered INTEGER NOT NULL DEFAULT 0,
    UNIQUE(daily_delivery_id, product_id)
);

-- Create Returns Table
CREATE TABLE IF NOT EXISTS returns (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    date DATE NOT NULL,
    return_count INTEGER NOT NULL DEFAULT 0,
    notes TEXT,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create Return Items Table
CREATE TABLE IF NOT EXISTS return_items (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    return_id UUID REFERENCES returns(id) ON DELETE CASCADE,
    product_id UUID REFERENCES products(id) ON DELETE CASCADE,
    quantity_returned INTEGER NOT NULL DEFAULT 0,
    UNIQUE(return_id, product_id)
);

-- Create Monthly Purchases Table
CREATE TABLE IF NOT EXISTS monthly_purchases (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    product_id UUID REFERENCES products(id) ON DELETE CASCADE,
    month INTEGER NOT NULL CHECK (month >= 1 AND month <= 12),
    year INTEGER NOT NULL,
    quantity_purchased INTEGER NOT NULL DEFAULT 0,
    cost_per_unit DECIMAL(10,2) NOT NULL DEFAULT 0,
    total_cost DECIMAL(12,2) GENERATED ALWAYS AS (quantity_purchased * cost_per_unit) STORED,
    purchase_date DATE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    UNIQUE(product_id, month, year)
);

-- Create Expenses Table
CREATE TABLE IF NOT EXISTS expenses (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    date DATE NOT NULL,
    item_name VARCHAR(255) NOT NULL,
    quantity INTEGER DEFAULT 1,
    amount DECIMAL(10,2) NOT NULL,
    invoice_url TEXT,
    category VARCHAR(100) DEFAULT 'General',
    notes TEXT,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create indexes for better performance
CREATE INDEX IF NOT EXISTS idx_products_name ON products(name);
CREATE INDEX IF NOT EXISTS idx_inventory_product_id ON inventory(product_id);
CREATE INDEX IF NOT EXISTS idx_daily_deliveries_date ON daily_deliveries(date);
CREATE INDEX IF NOT EXISTS idx_daily_delivery_items_delivery_id ON daily_delivery_items(daily_delivery_id);
CREATE INDEX IF NOT EXISTS idx_daily_delivery_items_product_id ON daily_delivery_items(product_id);
CREATE INDEX IF NOT EXISTS idx_returns_date ON returns(date);
CREATE INDEX IF NOT EXISTS idx_return_items_return_id ON return_items(return_id);
CREATE INDEX IF NOT EXISTS idx_return_items_product_id ON return_items(product_id);
CREATE INDEX IF NOT EXISTS idx_monthly_purchases_product_id ON monthly_purchases(product_id);
CREATE INDEX IF NOT EXISTS idx_monthly_purchases_month_year ON monthly_purchases(month, year);
CREATE INDEX IF NOT EXISTS idx_expenses_date ON expenses(date);
CREATE INDEX IF NOT EXISTS idx_expenses_category ON expenses(category);

-- Enable Row Level Security
ALTER TABLE products ENABLE ROW LEVEL SECURITY;
ALTER TABLE inventory ENABLE ROW LEVEL SECURITY;
ALTER TABLE daily_deliveries ENABLE ROW LEVEL SECURITY;
ALTER TABLE daily_delivery_items ENABLE ROW LEVEL SECURITY;
ALTER TABLE returns ENABLE ROW LEVEL SECURITY;
ALTER TABLE return_items ENABLE ROW LEVEL SECURITY;
ALTER TABLE monthly_purchases ENABLE ROW LEVEL SECURITY;
ALTER TABLE expenses ENABLE ROW LEVEL SECURITY;

-- Create policies (Allow all operations for authenticated users)
CREATE POLICY "Allow all operations for authenticated users" ON products
    FOR ALL USING (auth.role() = 'authenticated');

CREATE POLICY "Allow all operations for authenticated users" ON inventory
    FOR ALL USING (auth.role() = 'authenticated');

CREATE POLICY "Allow all operations for authenticated users" ON daily_deliveries
    FOR ALL USING (auth.role() = 'authenticated');

CREATE POLICY "Allow all operations for authenticated users" ON daily_delivery_items
    FOR ALL USING (auth.role() = 'authenticated');

CREATE POLICY "Allow all operations for authenticated users" ON returns
    FOR ALL USING (auth.role() = 'authenticated');

CREATE POLICY "Allow all operations for authenticated users" ON return_items
    FOR ALL USING (auth.role() = 'authenticated');

CREATE POLICY "Allow all operations for authenticated users" ON monthly_purchases
    FOR ALL USING (auth.role() = 'authenticated');

CREATE POLICY "Allow all operations for authenticated users" ON expenses
    FOR ALL USING (auth.role() = 'authenticated');

-- Insert your specific cosmetic/pharmaceutical products
INSERT INTO products (name, cost_per_unit) VALUES
('Survana Rosemary', 392),
('O. HS (New)', 480),
('O. Argireline Solution', 300),
('O. Ascorbic Acid', 280),
('O. Ascorbyl Glucoside Solution', 290),
('O. Niacinamide', 370),
('O. Retinol 1%', 280),
('O. Old Hair Serum', 450),
('O. GA 240 ml', 680),
('O. GA 100 ml', 420),
('O. SA 2%', 290),
('O. Alpha Arbutin 2%', 290),
('O. Eye Serum', 380),
('O. Caffeine Solution', 290),
('O. Sunscreen SPF 30', 400)
ON CONFLICT (name) DO NOTHING;

-- Initialize inventory for all products
INSERT INTO inventory (product_id, purchased, processed, delivered, returned)
SELECT id, 0, 0, 0, 0 FROM products
ON CONFLICT (product_id) DO NOTHING;

-- Create functions for inventory management
CREATE OR REPLACE FUNCTION update_inventory_delivered(p_product_id UUID, p_quantity INTEGER)
RETURNS VOID AS $$
BEGIN
  UPDATE inventory
  SET delivered = delivered + p_quantity,
      last_updated = NOW()
  WHERE product_id = p_product_id;

  -- Create inventory record if it doesn't exist
  IF NOT FOUND THEN
    INSERT INTO inventory (product_id, delivered, last_updated)
    VALUES (p_product_id, p_quantity, NOW())
    ON CONFLICT (product_id) DO UPDATE SET
      delivered = inventory.delivered + p_quantity,
      last_updated = NOW();
  END IF;
END;
$$ LANGUAGE plpgsql;

CREATE OR REPLACE FUNCTION update_inventory_returns(p_product_id UUID, p_quantity INTEGER)
RETURNS VOID AS $$
BEGIN
  UPDATE inventory
  SET returned = returned + p_quantity,
      last_updated = NOW()
  WHERE product_id = p_product_id;

  -- Create inventory record if it doesn't exist
  IF NOT FOUND THEN
    INSERT INTO inventory (product_id, returned, last_updated)
    VALUES (p_product_id, p_quantity, NOW())
    ON CONFLICT (product_id) DO UPDATE SET
      returned = inventory.returned + p_quantity,
      last_updated = NOW();
  END IF;
END;
$$ LANGUAGE plpgsql;

CREATE OR REPLACE FUNCTION update_inventory_purchased(p_product_id UUID, p_quantity INTEGER)
RETURNS VOID AS $$
BEGIN
  UPDATE inventory
  SET purchased = purchased + p_quantity,
      last_updated = NOW()
  WHERE product_id = p_product_id;

  -- Create inventory record if it doesn't exist
  IF NOT FOUND THEN
    INSERT INTO inventory (product_id, purchased, last_updated)
    VALUES (p_product_id, p_quantity, NOW())
    ON CONFLICT (product_id) DO UPDATE SET
      purchased = inventory.purchased + p_quantity,
      last_updated = NOW();
  END IF;
END;
$$ LANGUAGE plpgsql;
