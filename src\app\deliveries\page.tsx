'use client'

import { useEffect, useState } from 'react'
import Navigation from '@/components/Navigation'
import { supabase, type Delivery, type Product, type DeliveryItem } from '@/lib/supabase'
import { format } from 'date-fns'
import { PlusIcon, PencilIcon, TrashIcon } from '@heroicons/react/24/outline'

interface DeliveryFormData {
  date: string
  pickup_count: number
  delivered_count: number
  returned_count: number
  courier: string
  notes: string
  items: { product_id: string; quantity: number }[]
}

export default function DeliveriesPage() {
  const [deliveries, setDeliveries] = useState<Delivery[]>([])
  const [products, setProducts] = useState<Product[]>([])
  const [loading, setLoading] = useState(true)
  const [showForm, setShowForm] = useState(false)
  const [editingDelivery, setEditingDelivery] = useState<Delivery | null>(null)
  const [formData, setFormData] = useState<DeliveryFormData>({
    date: new Date().toISOString().split('T')[0],
    pickup_count: 0,
    delivered_count: 0,
    returned_count: 0,
    courier: '',
    notes: '',
    items: []
  })

  useEffect(() => {
    fetchData()
  }, [])

  const fetchData = async () => {
    try {
      const [deliveriesResult, productsResult] = await Promise.all([
        supabase.from('deliveries').select('*').order('date', { ascending: false }),
        supabase.from('products').select('*').order('name')
      ])

      if (deliveriesResult.data) setDeliveries(deliveriesResult.data)
      if (productsResult.data) setProducts(productsResult.data)
    } catch (error) {
      console.error('Error fetching data:', error)
    } finally {
      setLoading(false)
    }
  }

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    
    try {
      if (editingDelivery) {
        // Update existing delivery
        const { error } = await supabase
          .from('deliveries')
          .update({
            date: formData.date,
            pickup_count: formData.pickup_count,
            delivered_count: formData.delivered_count,
            returned_count: formData.returned_count,
            courier: formData.courier,
            notes: formData.notes,
            updated_at: new Date().toISOString()
          })
          .eq('id', editingDelivery.id)

        if (error) throw error
      } else {
        // Create new delivery
        const { data: delivery, error } = await supabase
          .from('deliveries')
          .insert({
            date: formData.date,
            pickup_count: formData.pickup_count,
            delivered_count: formData.delivered_count,
            returned_count: formData.returned_count,
            courier: formData.courier,
            notes: formData.notes
          })
          .select()
          .single()

        if (error) throw error

        // Insert delivery items
        if (delivery && formData.items.length > 0) {
          const deliveryItems = formData.items
            .filter(item => item.quantity > 0)
            .map(item => ({
              delivery_id: delivery.id,
              product_id: item.product_id,
              quantity: item.quantity
            }))

          if (deliveryItems.length > 0) {
            const { error: itemsError } = await supabase
              .from('delivery_items')
              .insert(deliveryItems)

            if (itemsError) throw itemsError
          }
        }
      }

      // Reset form and refresh data
      setFormData({
        date: new Date().toISOString().split('T')[0],
        pickup_count: 0,
        delivered_count: 0,
        returned_count: 0,
        courier: '',
        notes: '',
        items: []
      })
      setShowForm(false)
      setEditingDelivery(null)
      fetchData()
    } catch (error) {
      console.error('Error saving delivery:', error)
      alert('Error saving delivery. Please try again.')
    }
  }

  const handleEdit = (delivery: Delivery) => {
    setEditingDelivery(delivery)
    setFormData({
      date: delivery.date,
      pickup_count: delivery.pickup_count,
      delivered_count: delivery.delivered_count,
      returned_count: delivery.returned_count,
      courier: delivery.courier || '',
      notes: delivery.notes || '',
      items: []
    })
    setShowForm(true)
  }

  const handleDelete = async (id: string) => {
    if (!confirm('Are you sure you want to delete this delivery?')) return

    try {
      const { error } = await supabase
        .from('deliveries')
        .delete()
        .eq('id', id)

      if (error) throw error
      fetchData()
    } catch (error) {
      console.error('Error deleting delivery:', error)
      alert('Error deleting delivery. Please try again.')
    }
  }

  const addProductItem = () => {
    if (products.length > 0) {
      setFormData(prev => ({
        ...prev,
        items: [...prev.items, { product_id: products[0].id, quantity: 0 }]
      }))
    }
  }

  const updateProductItem = (index: number, field: 'product_id' | 'quantity', value: string | number) => {
    setFormData(prev => ({
      ...prev,
      items: prev.items.map((item, i) => 
        i === index ? { ...item, [field]: value } : item
      )
    }))
  }

  const removeProductItem = (index: number) => {
    setFormData(prev => ({
      ...prev,
      items: prev.items.filter((_, i) => i !== index)
    }))
  }

  if (loading) {
    return (
      <div className="min-h-screen bg-gray-50">
        <Navigation />
        <div className="flex items-center justify-center h-96">
          <div className="text-lg">Loading deliveries...</div>
        </div>
      </div>
    )
  }

  return (
    <div className="min-h-screen bg-gray-50">
      <Navigation />
      
      <div className="max-w-7xl mx-auto py-6 sm:px-6 lg:px-8">
        <div className="px-4 py-6 sm:px-0">
          <div className="flex justify-between items-center mb-8">
            <h1 className="text-3xl font-bold text-gray-900">Deliveries</h1>
            <button
              onClick={() => {
                setShowForm(true)
                setEditingDelivery(null)
                setFormData({
                  date: new Date().toISOString().split('T')[0],
                  pickup_count: 0,
                  delivered_count: 0,
                  returned_count: 0,
                  courier: '',
                  notes: '',
                  items: []
                })
              }}
              className="bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-md flex items-center space-x-2"
            >
              <PlusIcon className="h-5 w-5" />
              <span>New Delivery</span>
            </button>
          </div>

          {/* Delivery Form */}
          {showForm && (
            <div className="bg-white shadow rounded-lg mb-8">
              <div className="px-4 py-5 sm:p-6">
                <h3 className="text-lg leading-6 font-medium text-gray-900 mb-4">
                  {editingDelivery ? 'Edit Delivery' : 'New Delivery'}
                </h3>
                <form onSubmit={handleSubmit} className="space-y-6">
                  <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
                    <div>
                      <label className="block text-sm font-medium text-gray-700">Date</label>
                      <input
                        type="date"
                        value={formData.date}
                        onChange={(e) => setFormData(prev => ({ ...prev, date: e.target.value }))}
                        className="mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500"
                        required
                      />
                    </div>
                    <div>
                      <label className="block text-sm font-medium text-gray-700">Pickup Count</label>
                      <input
                        type="number"
                        min="0"
                        value={formData.pickup_count}
                        onChange={(e) => setFormData(prev => ({ ...prev, pickup_count: parseInt(e.target.value) || 0 }))}
                        className="mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500"
                        required
                      />
                    </div>
                    <div>
                      <label className="block text-sm font-medium text-gray-700">Delivered Count</label>
                      <input
                        type="number"
                        min="0"
                        value={formData.delivered_count}
                        onChange={(e) => setFormData(prev => ({ ...prev, delivered_count: parseInt(e.target.value) || 0 }))}
                        className="mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500"
                        required
                      />
                    </div>
                    <div>
                      <label className="block text-sm font-medium text-gray-700">Returned Count</label>
                      <input
                        type="number"
                        min="0"
                        value={formData.returned_count}
                        onChange={(e) => setFormData(prev => ({ ...prev, returned_count: parseInt(e.target.value) || 0 }))}
                        className="mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500"
                        required
                      />
                    </div>
                  </div>

                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div>
                      <label className="block text-sm font-medium text-gray-700">Courier</label>
                      <input
                        type="text"
                        value={formData.courier}
                        onChange={(e) => setFormData(prev => ({ ...prev, courier: e.target.value }))}
                        className="mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500"
                        placeholder="Courier name"
                      />
                    </div>
                    <div>
                      <label className="block text-sm font-medium text-gray-700">Notes</label>
                      <input
                        type="text"
                        value={formData.notes}
                        onChange={(e) => setFormData(prev => ({ ...prev, notes: e.target.value }))}
                        className="mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500"
                        placeholder="Additional notes"
                      />
                    </div>
                  </div>

                  {/* Product Items Section */}
                  {!editingDelivery && (
                    <div>
                      <div className="flex justify-between items-center mb-4">
                        <label className="block text-sm font-medium text-gray-700">Products</label>
                        <button
                          type="button"
                          onClick={addProductItem}
                          className="bg-green-600 hover:bg-green-700 text-white px-3 py-1 rounded text-sm"
                        >
                          Add Product
                        </button>
                      </div>
                      {formData.items.map((item, index) => (
                        <div key={index} className="flex items-center space-x-4 mb-2">
                          <select
                            value={item.product_id}
                            onChange={(e) => updateProductItem(index, 'product_id', e.target.value)}
                            className="flex-1 border-gray-300 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500"
                          >
                            {products.map(product => (
                              <option key={product.id} value={product.id}>
                                {product.name} ({product.sku})
                              </option>
                            ))}
                          </select>
                          <input
                            type="number"
                            min="0"
                            value={item.quantity}
                            onChange={(e) => updateProductItem(index, 'quantity', parseInt(e.target.value) || 0)}
                            className="w-24 border-gray-300 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500"
                            placeholder="Qty"
                          />
                          <button
                            type="button"
                            onClick={() => removeProductItem(index)}
                            className="text-red-600 hover:text-red-800"
                          >
                            <TrashIcon className="h-5 w-5" />
                          </button>
                        </div>
                      ))}
                    </div>
                  )}

                  <div className="flex justify-end space-x-3">
                    <button
                      type="button"
                      onClick={() => {
                        setShowForm(false)
                        setEditingDelivery(null)
                      }}
                      className="bg-gray-300 hover:bg-gray-400 text-gray-700 px-4 py-2 rounded-md"
                    >
                      Cancel
                    </button>
                    <button
                      type="submit"
                      className="bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-md"
                    >
                      {editingDelivery ? 'Update' : 'Create'} Delivery
                    </button>
                  </div>
                </form>
              </div>
            </div>
          )}

          {/* Deliveries List */}
          <div className="bg-white shadow rounded-lg">
            <div className="px-4 py-5 sm:p-6">
              <h3 className="text-lg leading-6 font-medium text-gray-900 mb-4">All Deliveries</h3>
              {deliveries.length > 0 ? (
                <div className="overflow-x-auto">
                  <table className="min-w-full divide-y divide-gray-200">
                    <thead className="bg-gray-50">
                      <tr>
                        <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Date</th>
                        <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Pickup</th>
                        <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Delivered</th>
                        <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Returned</th>
                        <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Success Rate</th>
                        <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Courier</th>
                        <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Actions</th>
                      </tr>
                    </thead>
                    <tbody className="bg-white divide-y divide-gray-200">
                      {deliveries.map((delivery) => {
                        const successRate = delivery.pickup_count > 0 
                          ? (delivery.delivered_count / delivery.pickup_count) * 100 
                          : 0
                        return (
                          <tr key={delivery.id}>
                            <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                              {format(new Date(delivery.date), 'MMM dd, yyyy')}
                            </td>
                            <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">{delivery.pickup_count}</td>
                            <td className="px-6 py-4 whitespace-nowrap text-sm text-green-600">{delivery.delivered_count}</td>
                            <td className="px-6 py-4 whitespace-nowrap text-sm text-red-600">{delivery.returned_count}</td>
                            <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">{successRate.toFixed(1)}%</td>
                            <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">{delivery.courier || '-'}</td>
                            <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                              <div className="flex space-x-2">
                                <button
                                  onClick={() => handleEdit(delivery)}
                                  className="text-blue-600 hover:text-blue-800"
                                >
                                  <PencilIcon className="h-4 w-4" />
                                </button>
                                <button
                                  onClick={() => handleDelete(delivery.id)}
                                  className="text-red-600 hover:text-red-800"
                                >
                                  <TrashIcon className="h-4 w-4" />
                                </button>
                              </div>
                            </td>
                          </tr>
                        )
                      })}
                    </tbody>
                  </table>
                </div>
              ) : (
                <p className="text-gray-500">No deliveries recorded yet. Create your first delivery above.</p>
              )}
            </div>
          </div>
        </div>
      </div>
    </div>
  )
}
