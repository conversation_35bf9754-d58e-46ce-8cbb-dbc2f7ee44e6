# Inventory Management System

A comprehensive, modern inventory and delivery management system built with Next.js, Supabase, and Tailwind CSS. Perfect for small to medium businesses looking to replace spreadsheet-based inventory tracking with a professional web application.

## 🚀 Features

### Core Functionality
- **Delivery Management**: Track pickups, deliveries, and returns with detailed analytics
- **Product Management**: Add, edit, and remove products with SKU tracking
- **Performance Analytics**: View success rates, monthly trends, and top-performing products
- **Data Export/Import**: Backup and restore your data with CSV and JSON exports
- **Responsive Design**: Works perfectly on desktop, tablet, and mobile devices

### Business Intelligence
- **Dashboard Analytics**: Real-time overview of your delivery performance
- **Success Rate Tracking**: Monitor delivery vs. return rates
- **Monthly Reports**: Track performance trends over time
- **Top Products Analysis**: Identify your best-selling items
- **Date Range Filtering**: Analyze specific time periods

## 🛠️ Technology Stack

- **Frontend**: Next.js 15 with React 18
- **Database**: Supabase (PostgreSQL)
- **Styling**: Tailwind CSS
- **Icons**: Heroicons
- **Date Handling**: date-fns
- **Authentication**: Supabase Auth (ready for future implementation)
- **Hosting**: Vercel (free tier available)

## 📋 Prerequisites

- Node.js 18+ installed on your machine
- A Supabase account (free tier available)
- Git for version control

## 🚀 Quick Start

### 1. Clone the Repository
```bash
git clone <your-repo-url>
cd inventory-management-system
```

### 2. Install Dependencies
```bash
npm install
```

### 3. Set Up Supabase Database

1. Go to [supabase.com](https://supabase.com) and create a free account
2. Create a new project
3. Go to the SQL Editor in your Supabase dashboard
4. Copy and paste the contents of `database-setup.sql` into the SQL Editor
5. Run the SQL commands to create the database tables

### 4. Configure Environment Variables

1. Copy the environment template:
```bash
cp .env.local.example .env.local
```

2. Update `.env.local` with your Supabase credentials:
```env
NEXT_PUBLIC_SUPABASE_URL=your_supabase_project_url
NEXT_PUBLIC_SUPABASE_ANON_KEY=your_supabase_anon_key
```

You can find these values in your Supabase dashboard under Settings → API.

### 5. Run the Development Server
```bash
npm run dev
```

Open [http://localhost:3000](http://localhost:3000) to view the application.

## 📊 Database Schema

The system uses four main tables:

- **products**: Store product information (SKU, name, category, price)
- **deliveries**: Track delivery records (date, pickup/delivered/returned counts)
- **delivery_items**: Link products to specific deliveries with quantities
- **Users**: Handle authentication (ready for future implementation)

## 🌐 Deployment

### Deploy to Vercel (Recommended - Free)

1. Push your code to GitHub
2. Go to [vercel.com](https://vercel.com) and sign up
3. Import your GitHub repository
4. Add your environment variables in the Vercel dashboard
5. Deploy!

### Alternative Hosting Options
- **Netlify**: Another free option with similar setup
- **Railway**: Good for full-stack applications
- **DigitalOcean App Platform**: Scalable option for growing businesses

## 📱 Usage Guide

### Managing Products
1. Navigate to the **Products** page
2. Click **"New Product"** to add items
3. Fill in SKU, name, category, and price
4. Edit or delete products as needed

### Recording Deliveries
1. Go to the **Deliveries** page
2. Click **"New Delivery"**
3. Enter the date and delivery counts
4. Add specific products and quantities
5. Include courier information and notes

### Viewing Reports
1. Visit the **Reports** page
2. Set your desired date range
3. View performance metrics and trends
4. Export data as CSV for external analysis

### Data Management
1. Go to **Settings** for data operations
2. Export all data as JSON backup
3. Import data from previous backups
4. Clear all data if needed (use with caution!)

## 🔧 Customization

### Adding New Features
The codebase is modular and easy to extend:
- Add new pages in `src/app/`
- Create reusable components in `src/components/`
- Extend database types in `src/lib/supabase.ts`

### Styling Customization
- Modify `tailwind.config.js` for theme changes
- Update `src/app/globals.css` for custom styles
- Components use Tailwind classes for easy customization

## 🔒 Security Features

- Row Level Security (RLS) enabled on all tables
- Environment variables for sensitive data
- Input validation and sanitization
- Prepared statements prevent SQL injection

## 📈 Performance Optimizations

- Server-side rendering with Next.js
- Optimized database queries with proper indexing
- Image optimization with Next.js Image component
- Lazy loading for better performance

## 🐛 Troubleshooting

### Common Issues

**Database Connection Errors**
- Verify your Supabase URL and API key
- Check that RLS policies are properly configured
- Ensure the database schema is created correctly

**Build Errors**
- Run `npm install` to ensure all dependencies are installed
- Check that all environment variables are set
- Verify Node.js version compatibility

**Styling Issues**
- Clear browser cache and restart development server
- Check for Tailwind CSS conflicts
- Verify all CSS imports are correct

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch (`git checkout -b feature/amazing-feature`)
3. Commit your changes (`git commit -m 'Add amazing feature'`)
4. Push to the branch (`git push origin feature/amazing-feature`)
5. Open a Pull Request

## 📄 License

This project is licensed under the MIT License - see the [LICENSE](LICENSE) file for details.

## 🙏 Acknowledgments

- Built with [Next.js](https://nextjs.org/)
- Database powered by [Supabase](https://supabase.com/)
- UI components styled with [Tailwind CSS](https://tailwindcss.com/)
- Icons from [Heroicons](https://heroicons.com/)

## 📞 Support

If you encounter any issues or have questions:
1. Check the troubleshooting section above
2. Review the Supabase and Next.js documentation
3. Create an issue in this repository

---

**Happy inventory managing! 📦✨**
